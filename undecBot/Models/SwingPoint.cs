using System;

namespace UndecBot.Models
{
    /// <summary>
    /// Represents a swing point in price action analysis
    /// </summary>
    public class SwingPoint
    {
        public int Index { get; }
        public double Price { get; }
        public SwingType Type { get; }
        public DateTime Time { get; }

        public SwingPoint(int index, double price, SwingType type, DateTime time)
        {
            Index = index;
            Price = price;
            Type = type;
            Time = time;
        }

        public SwingPoint(int index, double price, string type, DateTime time)
        {
            Index = index;
            Price = price;
            Type = type.ToLower() == "high" ? SwingType.High : SwingType.Low;
            Time = time;
        }

        public override bool Equals(object obj)
        {
            if (obj is SwingPoint other)
            {
                return Index == other.Index && 
                       Math.Abs(Price - other.Price) < 0.00001 && 
                       Type == other.Type;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Index, Price, Type);
        }

        public override string ToString()
        {
            return $"SwingPoint: {Type} at {Price} (Index: {Index})";
        }
    }
}
