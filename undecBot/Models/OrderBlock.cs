using System;

namespace UndecBot.Models
{
    /// <summary>
    /// Represents an order block in the market structure
    /// </summary>
    public class OrderBlock
    {
        public int Index { get; }
        public double Price { get; }
        public bool IsBullish { get; }
        public double InvalidationPrice { get; }
        public DateTime Time { get; }
        public bool IsActive { get; set; }

        public OrderBlock(int index, double price, bool isBullish, double invalidationPrice, DateTime time)
        {
            Index = index;
            Price = price;
            IsBullish = isBullish;
            InvalidationPrice = invalidationPrice;
            Time = time;
            IsActive = true;
        }

        public bool IsValidForEntry(double currentPrice, double currentHigh, double currentLow, double previousClose)
        {
            if (!IsActive) return false;

            if (IsBullish)
            {
                // For +OB (bullish order block): current price must be trading above it
                return currentLow > Price && currentLow >= InvalidationPrice;
            }
            else
            {
                // For -OB (bearish order block): current price must be trading below it
                return currentHigh < Price && currentHigh <= InvalidationPrice;
            }
        }

        public void Invalidate()
        {
            IsActive = false;
        }

        public override bool Equals(object obj)
        {
            if (obj is OrderBlock other)
            {
                return Index == other.Index && 
                       Math.Abs(Price - other.Price) < 0.00001 && 
                       IsBullish == other.IsBullish &&
                       Math.Abs(InvalidationPrice - other.InvalidationPrice) < 0.00001;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Index, Price, IsBullish, InvalidationPrice);
        }

        public override string ToString()
        {
            return $"OrderBlock: {(IsBullish ? "Bullish" : "Bearish")} at {Price:F5} (Index: {Index}, Active: {IsActive})";
        }
    }
}
