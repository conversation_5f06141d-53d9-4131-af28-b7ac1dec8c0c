using System;

namespace UndecBot.Models
{
    /// <summary>
    /// Represents a Fair Value Gap zone
    /// </summary>
    public class FVGZone
    {
        public int Index { get; }
        public double High { get; }
        public double Low { get; }
        public bool IsBullish { get; }
        public DateTime Time { get; }
        public bool IsActive { get; set; }

        public FVGZone(int index, double high, double low, bool isBullish, DateTime time)
        {
            Index = index;
            High = high;
            Low = low;
            IsBullish = isBullish;
            Time = time;
            IsActive = true;
        }

        public double GetMidPoint()
        {
            return (High + Low) / 2.0;
        }

        public double GetHeight()
        {
            return High - Low;
        }

        public bool ContainsPrice(double price)
        {
            return price >= Low && price <= High;
        }

        public override bool Equals(object obj)
        {
            if (obj is FVGZone other)
            {
                return Index == other.Index && 
                       Math.Abs(High - other.High) < 0.00001 && 
                       Math.Abs(Low - other.Low) < 0.00001 &&
                       IsBullish == other.IsBullish;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Index, High, Low, IsBullish);
        }

        public override string ToString()
        {
            return $"FVGZone: {(IsBullish ? "Bullish" : "Bearish")} [{Low:F5} - {High:F5}] (Index: {Index})";
        }
    }
}
