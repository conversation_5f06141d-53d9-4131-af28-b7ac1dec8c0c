using System;
using System.Collections.Generic;
using System.Linq;
using cAlgo.API;

namespace UndecBot.Services
{
    /// <summary>
    /// Service for executing trades
    /// </summary>
    public class TradingExecutionService
    {
        private readonly Robot _robot;
        private readonly string _symbolName;
        private readonly DataSeries _high;
        private readonly DataSeries _low;
        private readonly DataSeries _close;
        private readonly TimeSeries _time;

        public TradingExecutionService(Robot robot, string symbolName, DataSeries high, DataSeries low, DataSeries close, TimeSeries time)
        {
            _robot = robot;
            _symbolName = symbolName;
            _high = high;
            _low = low;
            _close = close;
            _time = time;
        }

        /// <summary>
        /// Executes a market order
        /// </summary>
        public TradeResult ExecuteMarketOrder(TradeType tradeType, double volume, int stopLossPips, int takeProfitPips, string comment)
        {
            try
            {
                var result = _robot.ExecuteMarketOrder(tradeType, _symbolName, volume, comment, stopLossPips, takeProfitPips);
                return result;
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error executing market order: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets current positions for a trade type
        /// </summary>
        public IEnumerable<Position> GetCurrentPositions(TradeType tradeType)
        {
            try
            {
                return _robot.Positions.Where(p => p.TradeType == tradeType && p.SymbolName == _symbolName);
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error getting current positions: {ex.Message}");
                return new List<Position>();
            }
        }

        /// <summary>
        /// Closes a position
        /// </summary>
        public TradeResult ClosePosition(Position position)
        {
            try
            {
                return _robot.ClosePosition(position);
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error closing position: {ex.Message}");
                return null;
            }
        }
    }
}