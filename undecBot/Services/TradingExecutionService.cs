using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using cAlgo.API;

namespace UndecBot.Services
{
    /// <summary>
    /// High-performance service for executing trades with caching and optimization
    /// </summary>
    public class TradingExecutionService
    {
        private readonly Robot _robot;
        private readonly string _symbolName;
        private readonly DataSeries _high;
        private readonly DataSeries _low;
        private readonly DataSeries _close;
        private readonly TimeSeries _time;

        // Performance optimization: Position caching
        private readonly ConcurrentDictionary<TradeType, List<Position>> _positionCache;
        private DateTime _lastPositionCacheUpdate;
        private readonly TimeSpan _cacheValidityPeriod = TimeSpan.FromSeconds(1); // Cache for 1 second

        // Performance optimization: Trade result caching
        private readonly ConcurrentDictionary<string, TradeResult> _tradeResultCache;
        private readonly object _cacheLock = new object();

        public TradingExecutionService(Robot robot, string symbolName, DataSeries high, DataSeries low, DataSeries close, TimeSeries time)
        {
            _robot = robot;
            _symbolName = symbolName;
            _high = high;
            _low = low;
            _close = close;
            _time = time;

            // Initialize performance caches
            _positionCache = new ConcurrentDictionary<TradeType, List<Position>>();
            _tradeResultCache = new ConcurrentDictionary<string, TradeResult>();
            _lastPositionCacheUpdate = DateTime.MinValue;
        }

        /// <summary>
        /// Executes a market order with performance optimization and caching
        /// </summary>
        public TradeResult ExecuteMarketOrder(TradeType tradeType, double volume, int stopLossPips, int takeProfitPips, string comment)
        {
            try
            {
                // Performance optimization: Create cache key for duplicate order detection
                var cacheKey = $"{tradeType}_{volume}_{stopLossPips}_{takeProfitPips}_{comment}_{DateTime.UtcNow:yyyyMMddHHmm}";

                // Check if we recently executed a similar order (prevent duplicate orders within same minute)
                if (_tradeResultCache.ContainsKey(cacheKey))
                {
                    _robot.Print($"⚠️ Duplicate order detected, using cached result for {comment}");
                    return _tradeResultCache[cacheKey];
                }

                var result = _robot.ExecuteMarketOrder(tradeType, _symbolName, volume, comment, stopLossPips, takeProfitPips);

                // Cache successful results
                if (result != null && result.IsSuccessful)
                {
                    _tradeResultCache.TryAdd(cacheKey, result);

                    // Invalidate position cache since we have a new position
                    InvalidatePositionCache();

                    _robot.Print($"✅ Order executed successfully: {comment} | Volume: {volume} | SL: {stopLossPips} | TP: {takeProfitPips}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error executing market order: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets current positions for a trade type with high-performance caching
        /// </summary>
        public IEnumerable<Position> GetCurrentPositions(TradeType tradeType)
        {
            try
            {
                // Performance optimization: Use cached positions if still valid
                if (IsPositionCacheValid() && _positionCache.ContainsKey(tradeType))
                {
                    return _positionCache[tradeType];
                }

                // Refresh cache for all trade types at once (more efficient)
                RefreshPositionCache();

                return _positionCache.GetValueOrDefault(tradeType, new List<Position>());
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error getting current positions: {ex.Message}");
                return new List<Position>();
            }
        }

        /// <summary>
        /// Closes a position with cache invalidation
        /// </summary>
        public TradeResult ClosePosition(Position position)
        {
            try
            {
                var result = _robot.ClosePosition(position);

                if (result != null && result.IsSuccessful)
                {
                    // Invalidate position cache since we closed a position
                    InvalidatePositionCache();
                    _robot.Print($"✅ Position closed successfully: {position.Label}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error closing position: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Performance optimization: Checks if position cache is still valid
        /// </summary>
        private bool IsPositionCacheValid()
        {
            return DateTime.UtcNow - _lastPositionCacheUpdate < _cacheValidityPeriod;
        }

        /// <summary>
        /// Performance optimization: Refreshes position cache for all trade types
        /// </summary>
        private void RefreshPositionCache()
        {
            lock (_cacheLock)
            {
                try
                {
                    // Clear existing cache
                    _positionCache.Clear();

                    // Get all positions for this symbol at once (more efficient than multiple queries)
                    var allPositions = _robot.Positions.Where(p => p.SymbolName == _symbolName).ToList();

                    // Group by trade type for fast lookup
                    var buyPositions = allPositions.Where(p => p.TradeType == TradeType.Buy).ToList();
                    var sellPositions = allPositions.Where(p => p.TradeType == TradeType.Sell).ToList();

                    _positionCache.TryAdd(TradeType.Buy, buyPositions);
                    _positionCache.TryAdd(TradeType.Sell, sellPositions);

                    _lastPositionCacheUpdate = DateTime.UtcNow;

                    // Performance logging
                    _robot.Print($"🔄 Position cache refreshed: {buyPositions.Count} Buy, {sellPositions.Count} Sell positions");
                }
                catch (Exception ex)
                {
                    _robot.Print($"❌ Error refreshing position cache: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Performance optimization: Invalidates position cache when positions change
        /// </summary>
        private void InvalidatePositionCache()
        {
            lock (_cacheLock)
            {
                _positionCache.Clear();
                _lastPositionCacheUpdate = DateTime.MinValue;
            }
        }

        /// <summary>
        /// Performance optimization: Cleans up old cache entries
        /// </summary>
        public void CleanupCache()
        {
            lock (_cacheLock)
            {
                try
                {
                    // Clean up trade result cache (keep only recent entries)
                    var cutoffTime = DateTime.UtcNow.AddMinutes(-5);
                    var keysToRemove = _tradeResultCache.Keys
                        .Where(key => ExtractTimeFromCacheKey(key) < cutoffTime)
                        .ToList();

                    foreach (var key in keysToRemove)
                    {
                        _tradeResultCache.TryRemove(key, out _);
                    }

                    if (keysToRemove.Count > 0)
                    {
                        _robot.Print($"🧹 Cleaned up {keysToRemove.Count} old cache entries");
                    }
                }
                catch (Exception ex)
                {
                    _robot.Print($"❌ Error cleaning up cache: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Extracts timestamp from cache key for cleanup purposes
        /// </summary>
        private static DateTime ExtractTimeFromCacheKey(string cacheKey)
        {
            try
            {
                var parts = cacheKey.Split('_');
                if (parts.Length > 0)
                {
                    var timeStr = parts[parts.Length - 1];
                    if (DateTime.TryParseExact(timeStr, "yyyyMMddHHmm", null, System.Globalization.DateTimeStyles.None, out DateTime result))
                    {
                        return result;
                    }
                }
            }
            catch
            {
                // Ignore parsing errors
            }

            return DateTime.MinValue;
        }

        /// <summary>
        /// Gets performance metrics for this service
        /// </summary>
        public (int CachedPositions, int CachedTradeResults, bool CacheValid) GetPerformanceMetrics()
        {
            var totalCachedPositions = _positionCache.Values.Sum(list => list.Count);
            var cachedTradeResults = _tradeResultCache.Count;
            var cacheValid = IsPositionCacheValid();

            return (totalCachedPositions, cachedTradeResults, cacheValid);
        }
    }
}