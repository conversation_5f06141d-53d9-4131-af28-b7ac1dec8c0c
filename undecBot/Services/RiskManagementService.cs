using System;
using cAlgo.API;

namespace UndecBot.Services
{
    /// <summary>
    /// Service for managing trading risk
    /// </summary>
    public class RiskManagementService
    {
        private readonly Robot _robot;
        private readonly string _symbolName;

        public RiskManagementService(Robot robot, string symbolName)
        {
            _robot = robot;
            _symbolName = symbolName;
        }

        /// <summary>
        /// Validates if a trade meets risk management criteria
        /// </summary>
        public bool ValidateTradeRisk(double lots, int sl, int tp, double maxRiskPerTrade, 
            double maxDailyDrawdown, int maxPositions, double minRiskReward)
        {
            try
            {
                // Check position count
                if (_robot.Positions.Count >= maxPositions)
                {
                    _robot.Print($"❌ Risk: Max positions ({maxPositions}) reached");
                    return false;
                }

                // Check risk/reward ratio
                if (tp > 0 && sl > 0)
                {
                    double riskRewardRatio = (double)tp / sl;
                    if (riskRewardRatio < minRiskReward)
                    {
                        _robot.Print($"❌ Risk: R/R ratio {riskRewardRatio:F2} below minimum {minRiskReward:F2}");
                        return false;
                    }
                }

                // Basic risk validation passed
                return true;
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Risk validation error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets daily P&L
        /// </summary>
        public double GetDailyPnL()
        {
            try
            {
                double dailyPnL = 0;
                var today = DateTime.Today;
                
                foreach (var position in _robot.Positions)
                {
                    if (position.EntryTime.Date == today)
                    {
                        dailyPnL += position.NetProfit;
                    }
                }
                
                return dailyPnL;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Gets daily drawdown percentage
        /// </summary>
        public double GetDailyDrawdownPercentage()
        {
            try
            {
                double dailyPnL = GetDailyPnL();
                double accountBalance = _robot.Account.Balance;
                
                if (accountBalance > 0 && dailyPnL < 0)
                {
                    return Math.Abs(dailyPnL / accountBalance * 100);
                }
                
                return 0;
            }
            catch
            {
                return 0;
            }
        }
    }
}
