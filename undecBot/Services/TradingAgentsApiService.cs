using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using cAlgo.API;
using UndecBot.Models;

namespace UndecBot.Services
{
    /// <summary>
    /// Service for communicating with the AgentServer TradingAgent API
    /// </summary>
    public class TradingAgentsApiService
    {
        private readonly HttpClient _httpClient;
        private readonly Robot _robot;
        private readonly string _baseUrl;
        private readonly int _timeoutSeconds;

        public TradingAgentsApiService(Robot robot, string baseUrl = "http://tradingagent.undeclab.com", int timeoutSeconds = 30)
        {
            _robot = robot;
            _baseUrl = baseUrl.TrimEnd('/');
            _timeoutSeconds = timeoutSeconds;

            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(_timeoutSeconds);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "UndecBot/1.0");
        }

        /// <summary>
        /// Analyzes a symbol using the TradingAgent API
        /// </summary>
        public async Task<TradingAgentAnalysisResponse> AnalyzeSymbolAsync(
            string symbol,
            bool includeNews = true,
            bool includeEconomicData = true,
            int newsLookbackDays = 7,
            bool forceRefresh = false)
        {
            try
            {
                var url = $"{_baseUrl}/analyze/symbol/{symbol.ToUpper()}";
                var queryParams = new List<string>();

                if (!includeNews) queryParams.Add("includeNews=false");
                if (!includeEconomicData) queryParams.Add("includeEconomicData=false");
                if (newsLookbackDays != 7) queryParams.Add($"newsLookbackDays={newsLookbackDays}");
                if (forceRefresh) queryParams.Add("forceRefresh=true");

                if (queryParams.Any())
                {
                    url += "?" + string.Join("&", queryParams);
                }

                _robot.Print($"🔍 Requesting TradingAgent analysis for {symbol}: {url}");

                var response = await _httpClient.PostAsync(url, null);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return ParseAnalysisResponse(content, symbol);
                }
                else
                {
                    _robot.Print($"❌ TradingAgent API error: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (HttpRequestException ex)
            {
                _robot.Print($"❌ Network error calling TradingAgent API: {ex.Message}");
            }
            catch (TaskCanceledException ex)
            {
                _robot.Print($"⏰ TradingAgent API timeout after {_timeoutSeconds}s: {ex.Message}");
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Unexpected error in TradingAgent API: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Analyzes market data directly using the TradingAgent API
        /// </summary>
        public async Task<TradingAgentAnalysisResponse> AnalyzeMarketDataAsync(MarketData marketData)
        {
            try
            {
                var url = $"{_baseUrl}/analyze";
                var json = SerializeMarketData(marketData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _robot.Print($"🔍 Sending market data to TradingAgent for {marketData.Symbol}");

                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return ParseAnalysisResponse(responseContent, marketData.Symbol);
                }
                else
                {
                    _robot.Print($"❌ TradingAgent API error: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error in TradingAgent API: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Checks the health of the TradingAgent API
        /// </summary>
        public async Task<bool> CheckHealthAsync()
        {
            try
            {
                var url = $"{_baseUrl}/analyze/health";
                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return ParseHealthResponse(content);
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Health check failed: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Parses the analysis response from JSON string
        /// </summary>
        private TradingAgentAnalysisResponse ParseAnalysisResponse(string jsonContent, string symbol)
        {
            try
            {
                // Look for analysis object in response
                var analysisMatch = Regex.Match(jsonContent, @"""analysis"":\s*({[^}]+})");
                string analysisJson = analysisMatch.Success ? analysisMatch.Groups[1].Value : jsonContent;

                var symbolValue = ExtractJsonValue(analysisJson, "symbol") ?? symbol;
                var bias = ExtractJsonValue(analysisJson, "bias") ?? "HOLD";
                var confidence = ParseDouble(ExtractJsonValue(analysisJson, "confidence")) ?? 0.0;
                var processingTime = ParseDouble(ExtractJsonValue(analysisJson, "processingTimeMs")) ?? 0.0;
                var timestamp = ParseDateTime(ExtractJsonValue(analysisJson, "timestamp")) ?? DateTime.UtcNow;

                return new TradingAgentAnalysisResponse
                {
                    Symbol = symbolValue,
                    Bias = bias,
                    Confidence = confidence,
                    ProcessingTimeMs = processingTime,
                    Timestamp = timestamp,
                    Details = ParseAgentDetails(jsonContent)
                };
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error parsing analysis response: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Parses health response from JSON string
        /// </summary>
        private bool ParseHealthResponse(string jsonContent)
        {
            try
            {
                var healthValue = ExtractJsonValue(jsonContent, "overallHealthy");
                return healthValue?.ToLower() == "true";
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Parses agent details from the API response
        /// </summary>
        private List<AgentDetail> ParseAgentDetails(string jsonContent)
        {
            var agentDetails = new List<AgentDetail>();

            try
            {
                // Look for details array
                var detailsMatch = Regex.Match(jsonContent, @"""details"":\s*\[([^\]]+)\]");
                if (detailsMatch.Success)
                {
                    var detailsContent = detailsMatch.Groups[1].Value;
                    var detailMatches = Regex.Matches(detailsContent, @"{[^}]+}");

                    foreach (Match detailMatch in detailMatches)
                    {
                        var detailJson = detailMatch.Value;
                        agentDetails.Add(new AgentDetail
                        {
                            Agent = ExtractJsonValue(detailJson, "agent") ?? "Unknown",
                            Insight = ExtractJsonValue(detailJson, "Insight") ?? ExtractJsonValue(detailJson, "insight") ?? "",
                            Confidence = ParseDouble(ExtractJsonValue(detailJson, "Confidence") ?? ExtractJsonValue(detailJson, "confidence")) ?? 0.0,
                            Model = ExtractJsonValue(detailJson, "model")
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error parsing agent details: {ex.Message}");
            }

            return agentDetails;
        }

        /// <summary>
        /// Serializes market data to JSON string
        /// </summary>
        private string SerializeMarketData(MarketData marketData)
        {
            var recentPricesJson = string.Join(",", marketData.RecentPrices.Select(p => p.ToString()));

            return $@"{{
                ""Symbol"": ""{marketData.Symbol}"",
                ""Price"": {marketData.Price},
                ""PERatio"": {marketData.PERatio},
                ""Sector"": ""{marketData.Sector}"",
                ""RawNews"": ""{EscapeJsonString(marketData.RawNews)}"",
                ""RSI"": {marketData.RSI},
                ""MACD"": {marketData.MACD},
                ""RecentPrices"": [{recentPricesJson}]
            }}";
        }

        /// <summary>
        /// Extracts a value from JSON string using regex
        /// </summary>
        private string ExtractJsonValue(string json, string key)
        {
            var pattern = $@"""{key}"":\s*""?([^"",}}\]]+)""?";
            var match = Regex.Match(json, pattern);
            return match.Success ? match.Groups[1].Value.Trim('"') : null;
        }

        /// <summary>
        /// Parses a double value from string
        /// </summary>
        private double? ParseDouble(string value)
        {
            if (string.IsNullOrEmpty(value)) return null;
            return double.TryParse(value, out var result) ? result : null;
        }

        /// <summary>
        /// Parses a DateTime value from string
        /// </summary>
        private DateTime? ParseDateTime(string value)
        {
            if (string.IsNullOrEmpty(value)) return null;
            return DateTime.TryParse(value, out var result) ? result : null;
        }

        /// <summary>
        /// Escapes special characters in JSON strings
        /// </summary>
        private string EscapeJsonString(string input)
        {
            if (string.IsNullOrEmpty(input)) return "";
            return input.Replace("\\", "\\\\").Replace("\"", "\\\"").Replace("\n", "\\n").Replace("\r", "\\r");
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}