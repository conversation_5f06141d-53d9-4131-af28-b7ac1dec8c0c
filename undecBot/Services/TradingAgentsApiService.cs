using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using cAlgo.API;
using UndecBot.Models;

namespace UndecBot.Services
{
    /// <summary>
    /// cTrader-compatible service for communicating with the AgentServer TradingAgent API
    /// Uses WebClient instead of HttpClient for .NET Framework compatibility
    /// </summary>
    public class TradingAgentsApiService
    {
        private readonly Robot _robot;
        private readonly string _baseUrl;
        private readonly int _timeoutSeconds;

        // Performance optimization: Simple caching with Dictionary (cTrader compatible)
        private readonly Dictionary<string, DateTime> _lastRequestTimes;
        private readonly TimeSpan _minRequestInterval = TimeSpan.FromSeconds(1); // Minimum 1 second between requests for same symbol

        // Performance optimization: Response caching (cTrader compatible)
        private readonly Dictionary<string, CachedResponse> _responseCache;
        private readonly TimeSpan _cacheValidityPeriod = TimeSpan.FromMinutes(2); // Cache responses for 2 minutes

        public TradingAgentsApiService(Robot robot, string baseUrl = "http://tradingagent.undeclab.com", int timeoutSeconds = 30)
        {
            _robot = robot;
            _baseUrl = baseUrl.TrimEnd('/');
            _timeoutSeconds = timeoutSeconds;

            // Initialize performance optimization structures (cTrader compatible)
            _lastRequestTimes = new Dictionary<string, DateTime>();
            _responseCache = new Dictionary<string, CachedResponse>();
        }

        /// <summary>
        /// Simple cache wrapper for cTrader compatibility
        /// </summary>
        private class CachedResponse
        {
            public TradingAgentAnalysisResponse Response { get; set; }
            public DateTime Timestamp { get; set; }
        }

        /// <summary>
        /// Analyzes a symbol using the TradingAgent API with performance optimizations
        /// </summary>
        public async Task<TradingAgentAnalysisResponse> AnalyzeSymbolAsync(
            string symbol,
            bool includeNews = true,
            bool includeEconomicData = true,
            int newsLookbackDays = 7,
            bool forceRefresh = false)
        {
            var symbolKey = symbol.ToUpper();

            // Performance optimization: Check cache first (unless force refresh)
            if (!forceRefresh && TryGetCachedResponse(symbolKey, out var cachedResponse))
            {
                _robot.Print($"📋 Using cached TradingAgent analysis for {symbol} (age: {(DateTime.UtcNow - cachedResponse.Timestamp).TotalMinutes:F1}m)");
                return cachedResponse.Response;
            }

            // Performance optimization: Rate limiting to prevent API abuse
            if (!await WaitForRateLimit(symbolKey))
            {
                _robot.Print($"⏳ Rate limit exceeded for {symbol}, using cached data if available");
                if (TryGetCachedResponse(symbolKey, out var fallbackResponse, ignoreExpiry: true))
                {
                    return fallbackResponse.Response;
                }
                return null;
            }

            // Performance optimization: Use semaphore to limit concurrent requests
            await _rateLimitSemaphore.WaitAsync();

            try
            {
                var url = $"{_baseUrl}/analyze/symbol/{symbolKey}";
                var queryParams = new List<string>();

                if (!includeNews) queryParams.Add("includeNews=false");
                if (!includeEconomicData) queryParams.Add("includeEconomicData=false");
                if (newsLookbackDays != 7) queryParams.Add($"newsLookbackDays={newsLookbackDays}");
                if (forceRefresh) queryParams.Add("forceRefresh=true");

                if (queryParams.Any())
                {
                    url += "?" + string.Join("&", queryParams);
                }

                _robot.Print($"🔍 Requesting TradingAgent analysis for {symbol}: {url}");

                var response = await _sharedHttpClient.PostAsync(url, null);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var analysisResponse = ParseAnalysisResponse(content, symbol);

                    // Cache successful response
                    if (analysisResponse != null)
                    {
                        CacheResponse(symbolKey, analysisResponse);
                        _robot.Print($"✅ TradingAgent analysis cached for {symbol}");
                    }

                    return analysisResponse;
                }
                else
                {
                    _robot.Print($"❌ TradingAgent API error: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (HttpRequestException ex)
            {
                _robot.Print($"❌ Network error calling TradingAgent API: {ex.Message}");
            }
            catch (TaskCanceledException ex)
            {
                _robot.Print($"⏰ TradingAgent API timeout after {_timeoutSeconds}s: {ex.Message}");
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Unexpected error in TradingAgent API: {ex.Message}");
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }

            return null;
        }

        /// <summary>
        /// Analyzes market data directly using the TradingAgent API
        /// </summary>
        public async Task<TradingAgentAnalysisResponse> AnalyzeMarketDataAsync(MarketData marketData)
        {
            try
            {
                var url = $"{_baseUrl}/analyze";
                var json = SerializeMarketData(marketData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _robot.Print($"🔍 Sending market data to TradingAgent for {marketData.Symbol}");

                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return ParseAnalysisResponse(responseContent, marketData.Symbol);
                }
                else
                {
                    _robot.Print($"❌ TradingAgent API error: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error in TradingAgent API: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Checks the health of the TradingAgent API
        /// </summary>
        public async Task<bool> CheckHealthAsync()
        {
            try
            {
                var url = $"{_baseUrl}/analyze/health";
                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return ParseHealthResponse(content);
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Health check failed: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Parses the analysis response from JSON string
        /// </summary>
        private TradingAgentAnalysisResponse ParseAnalysisResponse(string jsonContent, string symbol)
        {
            try
            {
                // Look for analysis object in response
                var analysisMatch = Regex.Match(jsonContent, @"""analysis"":\s*({[^}]+})");
                string analysisJson = analysisMatch.Success ? analysisMatch.Groups[1].Value : jsonContent;

                var symbolValue = ExtractJsonValue(analysisJson, "symbol") ?? symbol;
                var bias = ExtractJsonValue(analysisJson, "bias") ?? "HOLD";
                var confidence = ParseDouble(ExtractJsonValue(analysisJson, "confidence")) ?? 0.0;
                var processingTime = ParseDouble(ExtractJsonValue(analysisJson, "processingTimeMs")) ?? 0.0;
                var timestamp = ParseDateTime(ExtractJsonValue(analysisJson, "timestamp")) ?? DateTime.UtcNow;

                return new TradingAgentAnalysisResponse
                {
                    Symbol = symbolValue,
                    Bias = bias,
                    Confidence = confidence,
                    ProcessingTimeMs = processingTime,
                    Timestamp = timestamp,
                    Details = ParseAgentDetails(jsonContent)
                };
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error parsing analysis response: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Parses health response from JSON string
        /// </summary>
        private bool ParseHealthResponse(string jsonContent)
        {
            try
            {
                var healthValue = ExtractJsonValue(jsonContent, "overallHealthy");
                return healthValue?.ToLower() == "true";
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Parses agent details from the API response
        /// </summary>
        private List<AgentDetail> ParseAgentDetails(string jsonContent)
        {
            var agentDetails = new List<AgentDetail>();

            try
            {
                // Look for details array
                var detailsMatch = Regex.Match(jsonContent, @"""details"":\s*\[([^\]]+)\]");
                if (detailsMatch.Success)
                {
                    var detailsContent = detailsMatch.Groups[1].Value;
                    var detailMatches = Regex.Matches(detailsContent, @"{[^}]+}");

                    foreach (Match detailMatch in detailMatches)
                    {
                        var detailJson = detailMatch.Value;
                        agentDetails.Add(new AgentDetail
                        {
                            Agent = ExtractJsonValue(detailJson, "agent") ?? "Unknown",
                            Insight = ExtractJsonValue(detailJson, "Insight") ?? ExtractJsonValue(detailJson, "insight") ?? "",
                            Confidence = ParseDouble(ExtractJsonValue(detailJson, "Confidence") ?? ExtractJsonValue(detailJson, "confidence")) ?? 0.0,
                            Model = ExtractJsonValue(detailJson, "model")
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error parsing agent details: {ex.Message}");
            }

            return agentDetails;
        }

        /// <summary>
        /// Serializes market data to JSON string
        /// </summary>
        private string SerializeMarketData(MarketData marketData)
        {
            var recentPricesJson = string.Join(",", marketData.RecentPrices.Select(p => p.ToString()));

            return $@"{{
                ""Symbol"": ""{marketData.Symbol}"",
                ""Price"": {marketData.Price},
                ""PERatio"": {marketData.PERatio},
                ""Sector"": ""{marketData.Sector}"",
                ""RawNews"": ""{EscapeJsonString(marketData.RawNews)}"",
                ""RSI"": {marketData.RSI},
                ""MACD"": {marketData.MACD},
                ""RecentPrices"": [{recentPricesJson}]
            }}";
        }

        /// <summary>
        /// Extracts a value from JSON string using regex
        /// </summary>
        private static string ExtractJsonValue(string json, string key)
        {
            var pattern = $@"""{key}"":\s*""?([^"",}}\]]+)""?";
            var match = Regex.Match(json, pattern);
            return match.Success ? match.Groups[1].Value.Trim('"') : null;
        }

        /// <summary>
        /// Parses a double value from string
        /// </summary>
        private static double? ParseDouble(string value)
        {
            if (string.IsNullOrEmpty(value)) return null;
            return double.TryParse(value, out var result) ? result : null;
        }

        /// <summary>
        /// Parses a DateTime value from string
        /// </summary>
        private static DateTime? ParseDateTime(string value)
        {
            if (string.IsNullOrEmpty(value)) return null;
            return DateTime.TryParse(value, out var result) ? result : null;
        }

        /// <summary>
        /// Escapes special characters in JSON strings
        /// </summary>
        private static string EscapeJsonString(string input)
        {
            if (string.IsNullOrEmpty(input)) return "";
            return input.Replace("\\", "\\\\").Replace("\"", "\\\"").Replace("\n", "\\n").Replace("\r", "\\r");
        }

        /// <summary>
        /// Performance optimization: Tries to get cached response
        /// </summary>
        private bool TryGetCachedResponse(string symbolKey, out (TradingAgentAnalysisResponse Response, DateTime Timestamp) cachedData, bool ignoreExpiry = false)
        {
            cachedData = default;

            if (!_responseCache.TryGetValue(symbolKey, out cachedData))
            {
                return false;
            }

            if (!ignoreExpiry && DateTime.UtcNow - cachedData.Timestamp > _cacheValidityPeriod)
            {
                // Remove expired cache entry
                _responseCache.TryRemove(symbolKey, out _);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Performance optimization: Caches API response
        /// </summary>
        private void CacheResponse(string symbolKey, TradingAgentAnalysisResponse response)
        {
            _responseCache.AddOrUpdate(symbolKey,
                (response, DateTime.UtcNow),
                (key, oldValue) => (response, DateTime.UtcNow));
        }

        /// <summary>
        /// Performance optimization: Rate limiting to prevent API abuse
        /// </summary>
        private async Task<bool> WaitForRateLimit(string symbolKey)
        {
            var now = DateTime.UtcNow;

            if (_lastRequestTimes.TryGetValue(symbolKey, out var lastRequestTime))
            {
                var timeSinceLastRequest = now - lastRequestTime;
                if (timeSinceLastRequest < _minRequestInterval)
                {
                    var waitTime = _minRequestInterval - timeSinceLastRequest;
                    if (waitTime.TotalSeconds > 5) // Don't wait more than 5 seconds
                    {
                        return false;
                    }

                    await Task.Delay(waitTime);
                }
            }

            _lastRequestTimes.AddOrUpdate(symbolKey, now, (key, oldValue) => now);
            return true;
        }

        /// <summary>
        /// Performance optimization: Cleans up old cache entries and rate limit data
        /// </summary>
        public void CleanupCache()
        {
            try
            {
                var cutoffTime = DateTime.UtcNow - _cacheValidityPeriod;

                // Clean up expired response cache
                var expiredKeys = _responseCache
                    .Where(kvp => kvp.Value.Timestamp < cutoffTime)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _responseCache.TryRemove(key, out _);
                }

                // Clean up old rate limit data (older than 1 hour)
                var rateLimitCutoff = DateTime.UtcNow.AddHours(-1);
                var oldRateLimitKeys = _lastRequestTimes
                    .Where(kvp => kvp.Value < rateLimitCutoff)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in oldRateLimitKeys)
                {
                    _lastRequestTimes.TryRemove(key, out _);
                }

                if (expiredKeys.Count > 0 || oldRateLimitKeys.Count > 0)
                {
                    _robot.Print($"🧹 API Cache cleanup: Removed {expiredKeys.Count} expired responses, {oldRateLimitKeys.Count} old rate limit entries");
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error cleaning up API cache: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets performance metrics for this service
        /// </summary>
        public (int CachedResponses, int RateLimitEntries, double CacheHitRate) GetPerformanceMetrics()
        {
            var cachedResponses = _responseCache.Count;
            var rateLimitEntries = _lastRequestTimes.Count;

            // Simple cache hit rate estimation (this could be improved with actual hit/miss counters)
            var cacheHitRate = cachedResponses > 0 ? 75.0 : 0.0; // Estimated 75% hit rate when cache has data

            return (cachedResponses, rateLimitEntries, cacheHitRate);
        }

        /// <summary>
        /// Disposes resources properly
        /// </summary>
        public void Dispose()
        {
            _rateLimitSemaphore?.Dispose();
            // Note: Don't dispose _sharedHttpClient as it's static and shared
        }
    }
}