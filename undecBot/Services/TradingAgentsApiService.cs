using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using cAlgo.API;
using UndecBot.Models;

namespace UndecBot.Services
{
    /// <summary>
    /// cTrader-compatible service for TradingAgent API using built-in HTTP capabilities
    /// Uses the /analyze/ctrader/{symbol}?format=raw endpoint for flat JSON responses
    /// </summary>
    public class TradingAgentsApiService
    {
        private readonly Robot _robot;
        private readonly string _baseUrl;
        private readonly int _timeoutSeconds;
        
        // Performance optimization: Simple caching (cTrader compatible)
        private readonly Dictionary<string, DateTime> _lastRequestTimes;
        private readonly Dictionary<string, CachedResponse> _responseCache;
        private readonly TimeSpan _minRequestInterval = TimeSpan.FromSeconds(5); // 5 second minimum between requests
        private readonly TimeSpan _cacheValidityPeriod = TimeSpan.FromMinutes(3); // Cache for 3 minutes

        public TradingAgentsApiService(Robot robot, string baseUrl = "http://tradingagent.undeclab.com", int timeoutSeconds = 30)
        {
            _robot = robot;
            _baseUrl = baseUrl.TrimEnd('/');
            _timeoutSeconds = timeoutSeconds;
            
            _lastRequestTimes = new Dictionary<string, DateTime>();
            _responseCache = new Dictionary<string, CachedResponse>();
        }

        /// <summary>
        /// Simple cache wrapper for cTrader compatibility
        /// </summary>
        private class CachedResponse
        {
            public TradingAgentAnalysisResponse Response { get; set; }
            public DateTime Timestamp { get; set; }
        }

        /// <summary>
        /// Analyzes a symbol using the cTrader-optimized TradingAgent API endpoint
        /// Uses synchronous HTTP calls compatible with cTrader's .NET Framework
        /// </summary>
        public TradingAgentAnalysisResponse AnalyzeSymbol(
            string symbol,
            bool forceRefresh = false,
            string preferredDataSource = "Finnhub",
            string format = "summary")
        {
            var symbolKey = symbol.ToUpper();
            
            // Performance optimization: Check cache first (unless force refresh)
            if (!forceRefresh && TryGetCachedResponse(symbolKey, out var cachedResponse))
            {
                _robot.Print($"📋 Using cached TradingAgent analysis for {symbol} (age: {(DateTime.UtcNow - cachedResponse.Timestamp).TotalMinutes:F1}m)");
                return cachedResponse.Response;
            }

            // Performance optimization: Rate limiting to prevent API abuse
            if (!CheckRateLimit(symbolKey))
            {
                _robot.Print($"⏳ Rate limit exceeded for {symbol}, using cached data if available");
                if (TryGetCachedResponse(symbolKey, out var fallbackResponse, ignoreExpiry: true))
                {
                    return fallbackResponse.Response;
                }
                return null;
            }

            try
            {
                // Use the cTrader-specific endpoint with configurable format
                var url = $"{_baseUrl}/analyze/ctrader/{symbolKey}?format={format}";

                if (!string.IsNullOrEmpty(preferredDataSource))
                {
                    url += $"&preferredDataSource={preferredDataSource}";
                }

                if (forceRefresh)
                {
                    url += "&forceRefresh=true";
                }

                _robot.Print($"🔍 Requesting TradingAgent analysis for {symbol}: {url}");

                // Use cTrader's built-in HTTP capabilities
                var jsonResponse = MakeHttpRequest(url);
                
                if (!string.IsNullOrEmpty(jsonResponse))
                {
                    TradingAgentAnalysisResponse analysisResponse;

                    // Parse based on format
                    if (format == "summary")
                    {
                        analysisResponse = ParseSummaryResponse(jsonResponse, symbol);
                    }
                    else
                    {
                        analysisResponse = ParseCTraderResponse(jsonResponse, symbol);
                    }

                    // Cache successful response
                    if (analysisResponse != null)
                    {
                        CacheResponse(symbolKey, analysisResponse);
                        _robot.Print($"✅ TradingAgent analysis cached for {symbol} (format: {format})");
                    }

                    return analysisResponse;
                }
                else
                {
                    _robot.Print($"❌ Empty response from TradingAgent API for {symbol}");
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error calling TradingAgent API for {symbol}: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Makes HTTP request using cTrader's built-in capabilities
        /// </summary>
        private string MakeHttpRequest(string url)
        {
            try
            {
                // Use cTrader's Robot.Http property for HTTP requests
                var response = _robot.Http.Get(url);
                
                if (response.IsSuccessful)
                {
                    return response.ResponseBody;
                }
                else
                {
                    _robot.Print($"❌ HTTP Error: {response.StatusCode} - {response.ErrorMessage}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ HTTP Request failed: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Parses the flat JSON response from the cTrader endpoint
        /// </summary>
        private TradingAgentAnalysisResponse ParseCTraderResponse(string jsonResponse, string symbol)
        {
            try
            {
                // Parse the flat JSON structure from /analyze/ctrader/{symbol}?format=raw
                var response = new TradingAgentAnalysisResponse
                {
                    Symbol = ExtractJsonValue(jsonResponse, "symbol") ?? symbol,
                    Timestamp = DateTime.UtcNow,
                    ProcessingTimeMs = 0 // Not provided in cTrader format
                };

                // Extract key trading signals
                var recommendationScore = ParseDouble(ExtractJsonValue(jsonResponse, "recommendationScore")) ?? 5.0;
                var sentimentScore = ParseDouble(ExtractJsonValue(jsonResponse, "sentimentScore")) ?? 0.5;
                var riskLevel = ParseDouble(ExtractJsonValue(jsonResponse, "riskLevel")) ?? 2.0;

                // Convert to our format with improved thresholds
                if (recommendationScore >= 6.0)
                    response.Bias = "BUY";
                else if (recommendationScore <= 4.0)
                    response.Bias = "SELL";
                else
                    response.Bias = "HOLD";

                // Calculate confidence based on recommendation score and sentiment (improved formula)
                var scoreDeviation = Math.Abs(recommendationScore - 5.0) / 5.0; // 0.0 to 1.0
                var sentimentBoost = (sentimentScore * 0.5) + 0.5; // 0.5 to 1.0
                response.Confidence = Math.Min(1.0, scoreDeviation * sentimentBoost);

                // Add details for compatibility
                response.Details = new List<AgentDetail>
                {
                    new AgentDetail
                    {
                        Agent = "TradingAgent",
                        Insight = $"Recommendation: {recommendationScore:F1}/10, Risk: {riskLevel:F0}",
                        Confidence = response.Confidence,
                        Model = "cTrader-Optimized"
                    }
                };

                return response;
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error parsing cTrader response: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Parses the summary JSON response from the cTrader endpoint (RECOMMENDED)
        /// </summary>
        private TradingAgentAnalysisResponse ParseSummaryResponse(string jsonResponse, string symbol)
        {
            try
            {
                // Parse the summary JSON structure from /analyze/ctrader/{symbol}?format=summary
                var response = new TradingAgentAnalysisResponse
                {
                    Symbol = ExtractJsonValue(jsonResponse, "symbol") ?? symbol,
                    Timestamp = DateTime.UtcNow,
                    ProcessingTimeMs = 0 // Not provided in summary format
                };

                // Extract trading signals directly
                var signal = ExtractJsonValue(jsonResponse, "signal") ?? "HOLD";
                var confidence = ParseDouble(ExtractJsonValue(jsonResponse, "confidence")) ?? 5.0;
                var riskReward = ParseDouble(ExtractJsonValue(jsonResponse, "riskReward")) ?? 1.0;
                var trend = ExtractJsonValue(jsonResponse, "trend") ?? "NEUTRAL";
                var strength = ExtractJsonValue(jsonResponse, "strength") ?? "WEAK";

                // Use the direct signal from API
                response.Bias = signal.ToUpper();

                // Convert confidence (1-10 scale) to our 0-1 scale
                response.Confidence = Math.Min(1.0, confidence / 10.0);

                // Add enhanced details for summary format
                response.Details = new List<AgentDetail>
                {
                    new AgentDetail
                    {
                        Agent = "TradingAgent-Summary",
                        Insight = $"Signal: {signal}, Trend: {trend}, Strength: {strength}, R/R: {riskReward:F2}",
                        Confidence = response.Confidence,
                        Model = "cTrader-Summary-Optimized"
                    }
                };

                _robot.Print($"📊 Summary Analysis: {signal} signal with {confidence}/10 confidence, {trend} trend");
                return response;
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error parsing summary response: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Checks health of the TradingAgent API (synchronous)
        /// </summary>
        public bool CheckHealth()
        {
            try
            {
                var url = $"{_baseUrl}/analyze/health";
                var response = MakeHttpRequest(url);
                return !string.IsNullOrEmpty(response) && response.Contains("\"status\"");
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Health check failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Performance optimization: Checks rate limit (synchronous)
        /// </summary>
        private bool CheckRateLimit(string symbolKey)
        {
            var now = DateTime.UtcNow;
            
            if (_lastRequestTimes.ContainsKey(symbolKey))
            {
                var timeSinceLastRequest = now - _lastRequestTimes[symbolKey];
                if (timeSinceLastRequest < _minRequestInterval)
                {
                    return false; // Rate limited
                }
            }

            _lastRequestTimes[symbolKey] = now;
            return true;
        }

        /// <summary>
        /// Performance optimization: Tries to get cached response
        /// </summary>
        private bool TryGetCachedResponse(string symbolKey, out CachedResponse cachedData, bool ignoreExpiry = false)
        {
            cachedData = null;
            
            if (!_responseCache.ContainsKey(symbolKey))
            {
                return false;
            }

            cachedData = _responseCache[symbolKey];

            if (!ignoreExpiry && DateTime.UtcNow - cachedData.Timestamp > _cacheValidityPeriod)
            {
                // Remove expired cache entry
                _responseCache.Remove(symbolKey);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Performance optimization: Caches API response
        /// </summary>
        private void CacheResponse(string symbolKey, TradingAgentAnalysisResponse response)
        {
            _responseCache[symbolKey] = new CachedResponse
            {
                Response = response,
                Timestamp = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Performance optimization: Cleans up old cache entries and rate limit data
        /// </summary>
        public void CleanupCache()
        {
            try
            {
                var cutoffTime = DateTime.UtcNow - _cacheValidityPeriod;
                
                // Clean up expired response cache
                var expiredKeys = _responseCache
                    .Where(kvp => kvp.Value.Timestamp < cutoffTime)
                    .Select(kvp => kvp.Key)
                    .ToList();
                
                foreach (var key in expiredKeys)
                {
                    _responseCache.Remove(key);
                }

                // Clean up old rate limit data (older than 1 hour)
                var rateLimitCutoff = DateTime.UtcNow.AddHours(-1);
                var oldRateLimitKeys = _lastRequestTimes
                    .Where(kvp => kvp.Value < rateLimitCutoff)
                    .Select(kvp => kvp.Key)
                    .ToList();
                
                foreach (var key in oldRateLimitKeys)
                {
                    _lastRequestTimes.Remove(key);
                }

                if (expiredKeys.Count > 0 || oldRateLimitKeys.Count > 0)
                {
                    _robot.Print($"🧹 API Cache cleanup: Removed {expiredKeys.Count} expired responses, {oldRateLimitKeys.Count} old rate limit entries");
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error cleaning up API cache: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets performance metrics for this service
        /// </summary>
        public (int CachedResponses, int RateLimitEntries, double CacheHitRate) GetPerformanceMetrics()
        {
            var cachedResponses = _responseCache.Count;
            var rateLimitEntries = _lastRequestTimes.Count;
            
            // Simple cache hit rate estimation (this could be improved with actual hit/miss counters)
            var cacheHitRate = cachedResponses > 0 ? 75.0 : 0.0; // Estimated 75% hit rate when cache has data
            
            return (cachedResponses, rateLimitEntries, cacheHitRate);
        }

        /// <summary>
        /// Extracts a value from JSON string using simple regex (cTrader compatible)
        /// </summary>
        private static string ExtractJsonValue(string json, string key)
        {
            try
            {
                var pattern = $"\"{key}\"\\s*:\\s*\"?([^,}}\"]+)\"?";
                var match = Regex.Match(json, pattern);
                return match.Success ? match.Groups[1].Value.Trim('"') : null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Parses double value from string (cTrader compatible)
        /// </summary>
        private static double? ParseDouble(string value)
        {
            if (string.IsNullOrEmpty(value)) return null;
            return double.TryParse(value, out double result) ? result : (double?)null;
        }

        /// <summary>
        /// Parses DateTime value from string (cTrader compatible)
        /// </summary>
        private static DateTime? ParseDateTime(string value)
        {
            if (string.IsNullOrEmpty(value)) return null;
            return DateTime.TryParse(value, out DateTime result) ? result : (DateTime?)null;
        }
    }
}
