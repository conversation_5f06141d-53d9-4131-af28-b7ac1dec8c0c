using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using cAlgo.API;
using UndecBot.Models;

namespace UndecBot.Services
{
    /// <summary>
    /// Service for integrating TradingAgent decisions with the trading bot
    /// </summary>
    public class TradingAgentsIntegrationService
    {
        private readonly Robot _robot;
        private readonly string _symbolName;
        private readonly TradingAgentsApiService _apiService;
        private readonly Dictionary<string, TradingAgentAnalysisResponse> _analysisCache;
        private readonly Dictionary<string, DateTime> _cacheTimestamps;
        private readonly int _cacheExpiryMinutes = 5; // Cache analysis for 5 minutes

        public TradingAgentsIntegrationService(Robot robot, string symbolName, TradingAgentsApiService apiService)
        {
            _robot = robot;
            _symbolName = symbolName;
            _apiService = apiService;
            _analysisCache = new Dictionary<string, TradingAgentAnalysisResponse>();
            _cacheTimestamps = new Dictionary<string, DateTime>();
        }

        /// <summary>
        /// Gets a trading decision from TradingAgent for the specified ticker
        /// </summary>
        public async Task<TradingAgentsDecision> GetTradingDecisionAsync(string ticker)
        {
            try
            {
                var analysis = await GetCachedAnalysisAsync(ticker);
                if (analysis == null)
                {
                    return null;
                }

                // Convert TradingAgent analysis to TradingAgentsDecision format
                return new TradingAgentsDecision
                {
                    Ticker = ticker,
                    Action = analysis.Bias,
                    Confidence = analysis.Confidence,
                    RiskLevel = DetermineRiskLevel(analysis.Confidence),
                    Reasoning = GenerateReasoning(analysis),
                    Timestamp = analysis.Timestamp
                };
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error getting trading decision: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Determines if a trade should be executed based on TradingAgent analysis
        /// </summary>
        public async Task<bool> ShouldExecuteTradeAsync(OrderBlock orderBlock, double confidenceThreshold)
        {
            try
            {
                var ticker = _symbolName.Replace("_", "").ToUpper();
                var analysis = await GetCachedAnalysisAsync(ticker);

                if (analysis == null)
                {
                    _robot.Print($"⚠️ No TradingAgent analysis available for {ticker}");
                    return false;
                }

                // Check confidence threshold
                if (analysis.Confidence < confidenceThreshold)
                {
                    _robot.Print($"📊 TradingAgent confidence {analysis.Confidence:F2} below threshold {confidenceThreshold:F2}");
                    return false;
                }

                // Check if TradingAgent bias aligns with order block direction
                bool agentBullish = analysis.Bias.ToUpper() == "BUY";
                bool orderBlockBullish = orderBlock.IsBullish;

                if (agentBullish == orderBlockBullish)
                {
                    _robot.Print($"✅ TradingAgent alignment: {analysis.Bias} matches order block direction ({(orderBlockBullish ? "Bullish" : "Bearish")})");
                    _robot.Print($"🎯 Confidence: {analysis.Confidence:F2}, Processing time: {analysis.ProcessingTimeMs:F1}ms");

                    // Log agent insights
                    foreach (var detail in analysis.Details.Take(3)) // Show top 3 agents
                    {
                        _robot.Print($"   🤖 {detail.Agent}: {detail.Insight} (Confidence: {detail.Confidence:F2})");
                    }

                    return true;
                }
                else
                {
                    _robot.Print($"❌ TradingAgent conflict: {analysis.Bias} conflicts with order block direction ({(orderBlockBullish ? "Bullish" : "Bearish")})");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error in ShouldExecuteTradeAsync: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets risk-adjusted position size based on TradingAgent analysis
        /// </summary>
        public async Task<double> GetRiskAdjustedPositionSizeAsync(double baseLots, double riskPercentage)
        {
            try
            {
                var ticker = _symbolName.Replace("_", "").ToUpper();
                var analysis = await GetCachedAnalysisAsync(ticker);

                if (analysis == null)
                {
                    return baseLots; // Return base size if no analysis available
                }

                // Adjust position size based on confidence
                double confidenceMultiplier = analysis.Confidence;

                // Additional risk adjustment based on agent consensus
                double consensusScore = CalculateConsensusScore(analysis.Details);
                double riskMultiplier = confidenceMultiplier * consensusScore;

                // Cap the multiplier to prevent excessive risk
                riskMultiplier = Math.Min(riskMultiplier, 1.5); // Max 150% of base size
                riskMultiplier = Math.Max(riskMultiplier, 0.5); // Min 50% of base size

                double adjustedSize = baseLots * riskMultiplier;

                _robot.Print($"📊 Risk adjustment: Base {baseLots:F3} → Adjusted {adjustedSize:F3} (Confidence: {confidenceMultiplier:F2}, Consensus: {consensusScore:F2})");

                return adjustedSize;
            }
            catch (Exception ex)
            {
                _robot.Print($"❌ Error in GetRiskAdjustedPositionSizeAsync: {ex.Message}");
                return baseLots;
            }
        }

        /// <summary>
        /// Gets cached analysis or fetches new analysis if cache is expired
        /// </summary>
        private async Task<TradingAgentAnalysisResponse> GetCachedAnalysisAsync(string ticker)
        {
            var cacheKey = ticker.ToUpper();

            // Check if we have valid cached data
            if (_analysisCache.ContainsKey(cacheKey) && _cacheTimestamps.ContainsKey(cacheKey))
            {
                var cacheAge = DateTime.UtcNow - _cacheTimestamps[cacheKey];
                if (cacheAge.TotalMinutes < _cacheExpiryMinutes)
                {
                    _robot.Print($"📋 Using cached TradingAgent analysis for {ticker} (age: {cacheAge.TotalMinutes:F1}m)");
                    return _analysisCache[cacheKey];
                }
            }

            // Fetch new analysis
            _robot.Print($"🔄 Fetching fresh TradingAgent analysis for {ticker}...");
            var analysis = await _apiService.AnalyzeSymbolAsync(ticker, includeNews: true, includeEconomicData: true);

            if (analysis != null)
            {
                // Update cache
                _analysisCache[cacheKey] = analysis;
                _cacheTimestamps[cacheKey] = DateTime.UtcNow;
                _robot.Print($"✅ TradingAgent analysis cached for {ticker}");
            }

            return analysis;
        }

        /// <summary>
        /// Determines risk level based on confidence score
        /// </summary>
        private string DetermineRiskLevel(double confidence)
        {
            if (confidence >= 0.8) return "LOW";
            if (confidence >= 0.6) return "MEDIUM";
            return "HIGH";
        }

        /// <summary>
        /// Generates reasoning text from agent analysis
        /// </summary>
        private string GenerateReasoning(TradingAgentAnalysisResponse analysis)
        {
            var insights = analysis.Details
                .Where(d => !string.IsNullOrEmpty(d.Insight))
                .OrderByDescending(d => d.Confidence)
                .Take(3)
                .Select(d => $"{d.Agent}: {d.Insight}")
                .ToList();

            if (insights.Any())
            {
                return $"Multi-agent analysis ({analysis.Details.Count} agents): " + string.Join("; ", insights);
            }

            return $"Analysis bias: {analysis.Bias} with {analysis.Confidence:F2} confidence";
        }

        /// <summary>
        /// Calculates consensus score based on agent agreement
        /// </summary>
        private double CalculateConsensusScore(List<AgentDetail> details)
        {
            if (!details.Any()) return 0.5;

            // Count how many agents agree with the majority bias
            var buyCount = details.Count(d => d.Insight.ToUpper().Contains("BUY") || d.Insight.ToUpper().Contains("BULLISH"));
            var sellCount = details.Count(d => d.Insight.ToUpper().Contains("SELL") || d.Insight.ToUpper().Contains("BEARISH"));
            var holdCount = details.Count - buyCount - sellCount;

            var maxCount = Math.Max(Math.Max(buyCount, sellCount), holdCount);
            var consensusRatio = (double)maxCount / details.Count;

            // Weight by average confidence of agreeing agents
            var averageConfidence = details.Average(d => d.Confidence);

            return consensusRatio * averageConfidence;
        }

        /// <summary>
        /// Clears the analysis cache
        /// </summary>
        public void ClearCache()
        {
            _analysisCache.Clear();
            _cacheTimestamps.Clear();
            _robot.Print("🗑️ TradingAgent analysis cache cleared");
        }
    }
}