using System;
using System.Collections.Generic;
using System.Linq;
using cAlgo.API;
using UndecBot.Models;
using UndecBot.Configuration;

namespace UndecBot.Services
{
    /// <summary>
    /// Service responsible for detecting liquidity sweeps and order blocks
    /// </summary>
    public class LiquiditySweepDetectionService
    {
        private readonly List<OrderBlock> _orderBlocks;
        private readonly DataSeries _high;
        private readonly DataSeries _low;
        private readonly DataSeries _open;
        private readonly DataSeries _close;
        private readonly TimeSeries _time;

        public LiquiditySweepDetectionService(DataSeries high, DataSeries low, DataSeries open, DataSeries close, TimeSeries time)
        {
            _orderBlocks = new List<OrderBlock>();
            _high = high ?? throw new ArgumentNullException(nameof(high));
            _low = low ?? throw new ArgumentNullException(nameof(low));
            _open = open ?? throw new ArgumentNullException(nameof(open));
            _close = close ?? throw new ArgumentNullException(nameof(close));
            _time = time ?? throw new ArgumentNullException(nameof(time));
        }

        /// <summary>
        /// Gets all detected order blocks
        /// </summary>
        public IReadOnlyList<OrderBlock> OrderBlocks => _orderBlocks.AsReadOnly();

        /// <summary>
        /// Detects liquidity sweeps for a given swing point
        /// </summary>
        public (bool IsWickSweep, bool IsBreakout) DetectLiquiditySweeps(SwingPoint swingPoint)
        {
            if (swingPoint == null) return (false, false);

            bool isWickSweep = IsWickSweep(swingPoint);
            bool isBreakout = IsBreakOut(swingPoint);

            return (isWickSweep, isBreakout);
        }

        /// <summary>
        /// Checks for consecutive impulse pattern and creates order blocks
        /// </summary>
        public bool DetectOrderBlock(SwingPoint swingPoint)
        {
            if (swingPoint == null) return false;

            // Prevent out-of-bounds access
            if (swingPoint.Index - BotConfiguration.SwingPointRange < 0 || 
                swingPoint.Index + BotConfiguration.SwingPointRange >= _close.Count)
                return false;

            if (swingPoint.Type == SwingType.Low)
            {
                return CheckLowSwingForOrderBlock(swingPoint);
            }
            else
            {
                return CheckHighSwingForOrderBlock(swingPoint);
            }
        }

        /// <summary>
        /// Gets active order blocks for trading
        /// </summary>
        public IEnumerable<OrderBlock> GetActiveOrderBlocks()
        {
            return _orderBlocks.Where(ob => ob.IsActive);
        }

        /// <summary>
        /// Cleans up old order blocks to maintain performance
        /// </summary>
        public void CleanupOldOrderBlocks()
        {
            while (_orderBlocks.Count > BotConfiguration.MaxOrderBlocks)
            {
                _orderBlocks.RemoveAt(0);
            }
        }

        private bool IsWickSweep(SwingPoint swingPoint)
        {
            int lastBar = _close.Count - 2;
            if (lastBar <= swingPoint.Index) return false;

            bool isHighSweep = swingPoint.Type == SwingType.High &&
                               _high[lastBar] > swingPoint.Price &&
                               _close[lastBar] < swingPoint.Price;

            bool isLowSweep = swingPoint.Type == SwingType.Low &&
                              _low[lastBar] < swingPoint.Price &&
                              _close[lastBar] > swingPoint.Price;

            if (!(isHighSweep || isLowSweep))
                return false;

            // Verify this is the highest/lowest point in the range
            for (int i = swingPoint.Index + 1; i <= lastBar; i++)
            {
                if (swingPoint.Type == SwingType.High && _high[i] > _high[lastBar])
                    return false;
                if (swingPoint.Type == SwingType.Low && _low[i] < _low[lastBar])
                    return false;
            }

            return true;
        }

        private bool IsBreakOut(SwingPoint swingPoint)
        {
            int lastBar = _close.Count - BotConfiguration.BreakoutLookback;
            if (lastBar <= swingPoint.Index) return false;

            bool isHighBreakout = swingPoint.Type == SwingType.High && _high[lastBar] > swingPoint.Price;
            bool isLowBreakout = swingPoint.Type == SwingType.Low && _low[lastBar] < swingPoint.Price;

            if (!(isHighBreakout || isLowBreakout))
                return false;

            // Verify this is the highest/lowest point in the range
            for (int i = swingPoint.Index + 1; i <= lastBar; i++)
            {
                if (swingPoint.Type == SwingType.High && _high[i] > _high[lastBar])
                    return false;
                if (swingPoint.Type == SwingType.Low && _low[i] < _low[lastBar])
                    return false;
            }

            return true;
        }

        private bool CheckLowSwingForOrderBlock(SwingPoint swingPoint)
        {
            double bearOpen = 0;
            int consecutiveBear = 0;
            int lastBearIndex = -1;
            bool foundImperfect = false;

            // First pass: Check for consecutive bearish bars
            for (int i = swingPoint.Index - 1; i >= 0 && consecutiveBear < BotConfiguration.MaxConsecutiveBars; i--)
            {
                if (_open[i] > _close[i]) // Bearish bar
                {
                    consecutiveBear++;
                    bearOpen = Math.Max(_open[i], _high[i]);
                    lastBearIndex = i;
                }
                else
                {
                    if (consecutiveBear >= BotConfiguration.MinConsecutiveBars) break;

                    if (!foundImperfect)
                    {
                        // Look for imperfect pattern
                        var (tempCons, tempLastIndex, tempBearOpen) = FindConsecutiveBearishBars(i - 1);

                        if (tempCons >= BotConfiguration.MinConsecutiveBars)
                        {
                            consecutiveBear = tempCons;
                            bearOpen = tempBearOpen;
                            lastBearIndex = tempLastIndex;
                            foundImperfect = true;
                            break;
                        }
                        else
                            break;
                    }
                    else break;
                }
            }

            // If we found the required bearish pattern, look for bullish impulse
            if (consecutiveBear >= BotConfiguration.MinConsecutiveBars)
            {
                return CheckForBullishImpulse(swingPoint, bearOpen, lastBearIndex);
            }

            return false;
        }

        private bool CheckHighSwingForOrderBlock(SwingPoint swingPoint)
        {
            double bullOpen = 0;
            int consecutiveBull = 0;
            int lastBullIndex = -1;
            bool foundImperfect = false;

            for (int i = swingPoint.Index - 1; i >= 0 && consecutiveBull < BotConfiguration.MaxConsecutiveBars; i--)
            {
                if (_open[i] < _close[i]) // Bullish
                {
                    consecutiveBull++;
                    bullOpen = Math.Min(_open[i], _low[i]);
                    lastBullIndex = i;
                }
                else // Imperfect bar
                {
                    if (consecutiveBull >= BotConfiguration.MinConsecutiveBars) break;

                    if (!foundImperfect)
                    {
                        var (tempCons, tempLastIndex, tempBullOpen) = FindConsecutiveBullishBars(i - 1);

                        if (tempCons >= BotConfiguration.MinConsecutiveBars)
                        {
                            consecutiveBull = tempCons;
                            bullOpen = tempBullOpen;
                            lastBullIndex = tempLastIndex;
                            foundImperfect = true;
                            break;
                        }
                        else break;
                    }
                    else break;
                }
            }

            if (consecutiveBull >= BotConfiguration.MinConsecutiveBars)
            {
                return CheckForBearishImpulse(swingPoint, bullOpen, lastBullIndex);
            }

            return false;
        }

        private (int count, int lastIndex, double price) FindConsecutiveBearishBars(int startIndex)
        {
            int tempCons = 0;
            int tempLastIndex = -1;
            double tempBearOpen = 0;

            for (int j = startIndex; j >= 0 && tempCons < BotConfiguration.MaxConsecutiveBars; j--)
            {
                if (_open[j] > _close[j])
                {
                    tempCons++;
                    tempBearOpen = Math.Max(_open[j], _high[j]);
                    tempLastIndex = j;
                }
                else
                    break;
            }

            return (tempCons, tempLastIndex, tempBearOpen);
        }

        private (int count, int lastIndex, double price) FindConsecutiveBullishBars(int startIndex)
        {
            int tempCons = 0;
            int tempLastIndex = -1;
            double tempBullOpen = 0;

            for (int j = startIndex; j >= 0 && tempCons < BotConfiguration.MaxConsecutiveBars; j--)
            {
                if (_open[j] < _close[j])
                {
                    tempCons++;
                    tempBullOpen = Math.Min(_open[j], _low[j]);
                    tempLastIndex = j;
                }
                else break;
            }

            return (tempCons, tempLastIndex, tempBullOpen);
        }

        private bool CheckForBullishImpulse(SwingPoint swingPoint, double bearOpen, int lastBearIndex)
        {
            for (int x = swingPoint.Index + 1; x <= swingPoint.Index + BotConfiguration.MaxImpulseLookahead && x < _close.Count; x++)
            {
                if (_open[x] < _close[x] && _close[x] > bearOpen) // Bullish bar with close > bearOpen
                {
                    var orderBlock = new OrderBlock(lastBearIndex, bearOpen, true, _low[swingPoint.Index], _time[lastBearIndex]);

                    if (!_orderBlocks.Any(ob => ob.Equals(orderBlock)))
                    {
                        _orderBlocks.Add(orderBlock);
                        CleanupOldOrderBlocks();
                    }
                    return true;
                }
            }
            return false;
        }

        private bool CheckForBearishImpulse(SwingPoint swingPoint, double bullOpen, int lastBullIndex)
        {
            for (int x = swingPoint.Index + 1; x <= swingPoint.Index + BotConfiguration.MaxImpulseLookahead && x < _close.Count; x++)
            {
                if (_open[x] > _close[x] && _close[x] < bullOpen) // Bearish bar with close < bullOpen
                {
                    var orderBlock = new OrderBlock(lastBullIndex, bullOpen, false, _high[swingPoint.Index], _time[lastBullIndex]);

                    if (!_orderBlocks.Any(ob => ob.Equals(orderBlock)))
                    {
                        _orderBlocks.Add(orderBlock);
                        CleanupOldOrderBlocks();
                    }
                    return true;
                }
            }
            return false;
        }
    }
}
