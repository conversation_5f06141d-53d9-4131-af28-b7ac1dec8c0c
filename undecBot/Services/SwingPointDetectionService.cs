using System;
using System.Collections.Generic;
using System.Linq;
using cAlgo.API;
using UndecBot.Models;
using UndecBot.Configuration;

namespace UndecBot.Services
{
    /// <summary>
    /// High-performance service for detecting swing points with circular buffer optimization
    /// </summary>
    public class SwingPointDetectionService
    {
        // Performance optimization: Use circular buffer instead of growing list
        private readonly SwingPoint[] _swingPointBuffer;
        private int _bufferIndex;
        private int _bufferCount;
        private readonly int _maxSwingPoints;

        private readonly DataSeries _high;
        private readonly DataSeries _low;
        private readonly TimeSeries _time;

        // Performance optimization: Cache for expensive calculations
        private readonly Dictionary<int, (double? High, double? Low)> _calculationCache;
        private int _lastProcessedIndex = -1;

        public SwingPointDetectionService(DataSeries high, DataSeries low, TimeSeries time)
        {
            _high = high ?? throw new ArgumentNullException(nameof(high));
            _low = low ?? throw new ArgumentNullException(nameof(low));
            _time = time ?? throw new ArgumentNullException(nameof(time));

            // Performance optimization: Initialize circular buffer
            _maxSwingPoints = BotConfiguration.MaxSwingPoints;
            _swingPointBuffer = new SwingPoint[_maxSwingPoints];
            _bufferIndex = 0;
            _bufferCount = 0;

            // Performance optimization: Initialize calculation cache
            _calculationCache = new Dictionary<int, (double?, double?)>();
        }

        /// <summary>
        /// Gets all detected swing points (performance optimized with lazy enumeration)
        /// </summary>
        public IEnumerable<SwingPoint> SwingPoints
        {
            get
            {
                // Performance optimization: Lazy enumeration of circular buffer
                for (int i = 0; i < _bufferCount; i++)
                {
                    var actualIndex = (_bufferIndex - _bufferCount + i + _maxSwingPoints) % _maxSwingPoints;
                    yield return _swingPointBuffer[actualIndex];
                }
            }
        }

        /// <summary>
        /// Gets swing points as a list (use sparingly for performance)
        /// </summary>
        public IReadOnlyList<SwingPoint> SwingPointsList => SwingPoints.ToList().AsReadOnly();

        /// <summary>
        /// Detects swing points at the specified index with performance optimizations
        /// </summary>
        /// <param name="barsCount">Total number of bars available</param>
        public void DetectSwingPoints(int barsCount)
        {
            int idx = barsCount - BotConfiguration.MaxLookbackBars;
            if (idx < 0 || idx <= _lastProcessedIndex) return;

            // Performance optimization: Use cached calculations if available
            if (!_calculationCache.TryGetValue(idx, out var cached))
            {
                var swingHigh = GetSwingHigh(idx);
                var swingLow = GetSwingLow(idx);
                cached = (swingHigh, swingLow);

                // Cache the calculation (limit cache size)
                if (_calculationCache.Count < 100)
                {
                    _calculationCache[idx] = cached;
                }
            }

            if (cached.High.HasValue)
            {
                var swingPoint = new SwingPoint(idx, cached.High.Value, SwingType.High, _time[idx]);
                AddSwingPointToBuffer(swingPoint);
            }

            if (cached.Low.HasValue)
            {
                var swingPoint = new SwingPoint(idx, cached.Low.Value, SwingType.Low, _time[idx]);
                AddSwingPointToBuffer(swingPoint);
            }

            _lastProcessedIndex = idx;
        }

        /// <summary>
        /// Gets swing points of a specific type (performance optimized)
        /// </summary>
        public IEnumerable<SwingPoint> GetSwingPointsByType(SwingType type)
        {
            // Performance optimization: Filter during enumeration to avoid creating intermediate collections
            return SwingPoints.Where(sp => sp.Type == type);
        }

        /// <summary>
        /// Gets the most recent swing point of a specific type (performance optimized)
        /// </summary>
        public SwingPoint GetLatestSwingPoint(SwingType type)
        {
            // Performance optimization: Iterate backwards through buffer for latest point
            SwingPoint latest = null;
            int latestIndex = -1;

            for (int i = 0; i < _bufferCount; i++)
            {
                var actualIndex = (_bufferIndex - 1 - i + _maxSwingPoints) % _maxSwingPoints;
                var swingPoint = _swingPointBuffer[actualIndex];

                if (swingPoint.Type == type && swingPoint.Index > latestIndex)
                {
                    latest = swingPoint;
                    latestIndex = swingPoint.Index;
                    break; // Since we're iterating backwards, first match is the latest
                }
            }

            return latest;
        }

        /// <summary>
        /// Clears old swing points to maintain performance (optimized for circular buffer)
        /// </summary>
        public void CleanupOldSwingPoints()
        {
            // Performance optimization: Circular buffer automatically handles cleanup
            // Just clean up the calculation cache
            if (_calculationCache.Count > 200)
            {
                var oldestKeys = _calculationCache.Keys.OrderBy(k => k).Take(100).ToList();
                foreach (var key in oldestKeys)
                {
                    _calculationCache.Remove(key);
                }
            }
        }

        /// <summary>
        /// Performance optimized: Adds swing point to circular buffer
        /// </summary>
        private void AddSwingPointToBuffer(SwingPoint swingPoint)
        {
            // Check for duplicate at the same index and type
            bool isDuplicate = false;
            for (int i = 0; i < _bufferCount; i++)
            {
                var actualIndex = (_bufferIndex - _bufferCount + i + _maxSwingPoints) % _maxSwingPoints;
                var existing = _swingPointBuffer[actualIndex];
                if (existing.Index == swingPoint.Index && existing.Type == swingPoint.Type)
                {
                    isDuplicate = true;
                    break;
                }
            }

            if (!isDuplicate)
            {
                _swingPointBuffer[_bufferIndex] = swingPoint;
                _bufferIndex = (_bufferIndex + 1) % _maxSwingPoints;

                if (_bufferCount < _maxSwingPoints)
                {
                    _bufferCount++;
                }
            }
        }

        private double? GetSwingHigh(int index)
        {
            if (index < BotConfiguration.SwingPointRange || index + 2 >= _high.Count) 
                return null;

            double indexHigh = _high[index];

            // Check left side (N bars before)
            for (int i = index - BotConfiguration.SwingPointRange; i <= index; i++)
            {
                if (_high[i] > indexHigh)
                    return null;
            }

            // Check right side (2 bars after)
            for (int i = index; i < index + 2 && i < _high.Count; i++)
            {
                if (_high[i] > indexHigh)
                    return null;
            }

            return indexHigh;
        }

        private double? GetSwingLow(int index)
        {
            if (index < BotConfiguration.SwingPointRange || index + 2 >= _low.Count) 
                return null;

            double indexLow = _low[index];

            // Check left side (N bars before)
            for (int i = index - BotConfiguration.SwingPointRange; i <= index; i++)
            {
                if (_low[i] < indexLow)
                    return null;
            }

            // Check right side (2 bars after)
            for (int i = index; i < index + 2 && i < _low.Count; i++)
            {
                if (_low[i] < indexLow)
                    return null;
            }

            return indexLow;
        }

        /// <summary>
        /// Gets performance metrics for this service
        /// </summary>
        public (int BufferCount, int CacheSize, double BufferUtilization) GetPerformanceMetrics()
        {
            var bufferUtilization = _bufferCount > 0 ? (_bufferCount / (double)_maxSwingPoints) * 100.0 : 0.0;
            return (_bufferCount, _calculationCache.Count, bufferUtilization);
        }

        /// <summary>
        /// Gets count of swing points by type (performance optimized)
        /// </summary>
        public (int HighCount, int LowCount) GetSwingPointCounts()
        {
            int highCount = 0, lowCount = 0;

            for (int i = 0; i < _bufferCount; i++)
            {
                var actualIndex = (_bufferIndex - _bufferCount + i + _maxSwingPoints) % _maxSwingPoints;
                var swingPoint = _swingPointBuffer[actualIndex];

                if (swingPoint.Type == SwingType.High)
                    highCount++;
                else if (swingPoint.Type == SwingType.Low)
                    lowCount++;
            }

            return (highCount, lowCount);
        }

        /// <summary>
        /// Clears all cached data for memory optimization
        /// </summary>
        public void ClearCache()
        {
            _calculationCache.Clear();
            _lastProcessedIndex = -1;
        }
    }
}
