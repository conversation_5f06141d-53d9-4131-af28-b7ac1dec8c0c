using System;
using System.Collections.Generic;
using System.Linq;
using cAlgo.API;
using UndecBot.Models;
using UndecBot.Configuration;

namespace UndecBot.Services
{
    /// <summary>
    /// Service responsible for detecting swing points in price action
    /// </summary>
    public class SwingPointDetectionService
    {
        private readonly List<SwingPoint> _swingPoints;
        private readonly DataSeries _high;
        private readonly DataSeries _low;
        private readonly TimeSeries _time;

        public SwingPointDetectionService(DataSeries high, DataSeries low, TimeSeries time)
        {
            _swingPoints = new List<SwingPoint>();
            _high = high ?? throw new ArgumentNullException(nameof(high));
            _low = low ?? throw new ArgumentNullException(nameof(low));
            _time = time ?? throw new ArgumentNullException(nameof(time));
        }

        /// <summary>
        /// Gets all detected swing points
        /// </summary>
        public IReadOnlyList<SwingPoint> SwingPoints => _swingPoints.AsReadOnly();

        /// <summary>
        /// Detects swing points at the specified index
        /// </summary>
        /// <param name="barsCount">Total number of bars available</param>
        public void DetectSwingPoints(int barsCount)
        {
            int idx = barsCount - BotConfiguration.MaxLookbackBars;
            if (idx < 0) return;

            var swingHigh = GetSwingHigh(idx);
            var swingLow = GetSwingLow(idx);

            if (swingHigh.HasValue)
            {
                var swingPoint = new SwingPoint(idx, swingHigh.Value, SwingType.High, _time[idx]);
                AddSwingPoint(swingPoint);
            }

            if (swingLow.HasValue)
            {
                var swingPoint = new SwingPoint(idx, swingLow.Value, SwingType.Low, _time[idx]);
                AddSwingPoint(swingPoint);
            }
        }

        /// <summary>
        /// Gets swing points of a specific type
        /// </summary>
        public IEnumerable<SwingPoint> GetSwingPointsByType(SwingType type)
        {
            return _swingPoints.Where(sp => sp.Type == type);
        }

        /// <summary>
        /// Gets the most recent swing point of a specific type
        /// </summary>
        public SwingPoint GetLatestSwingPoint(SwingType type)
        {
            return _swingPoints.Where(sp => sp.Type == type).OrderByDescending(sp => sp.Index).FirstOrDefault();
        }

        /// <summary>
        /// Clears old swing points to maintain performance
        /// </summary>
        public void CleanupOldSwingPoints()
        {
            while (_swingPoints.Count > BotConfiguration.MaxSwingPoints)
            {
                _swingPoints.RemoveAt(0);
            }
        }

        private void AddSwingPoint(SwingPoint swingPoint)
        {
            // Avoid duplicate swing points at the same index
            if (!_swingPoints.Any(sp => sp.Index == swingPoint.Index && sp.Type == swingPoint.Type))
            {
                _swingPoints.Add(swingPoint);
                CleanupOldSwingPoints();
            }
        }

        private double? GetSwingHigh(int index)
        {
            if (index < BotConfiguration.SwingPointRange || index + 2 >= _high.Count) 
                return null;

            double indexHigh = _high[index];

            // Check left side (N bars before)
            for (int i = index - BotConfiguration.SwingPointRange; i <= index; i++)
            {
                if (_high[i] > indexHigh)
                    return null;
            }

            // Check right side (2 bars after)
            for (int i = index; i < index + 2 && i < _high.Count; i++)
            {
                if (_high[i] > indexHigh)
                    return null;
            }

            return indexHigh;
        }

        private double? GetSwingLow(int index)
        {
            if (index < BotConfiguration.SwingPointRange || index + 2 >= _low.Count) 
                return null;

            double indexLow = _low[index];

            // Check left side (N bars before)
            for (int i = index - BotConfiguration.SwingPointRange; i <= index; i++)
            {
                if (_low[i] < indexLow)
                    return null;
            }

            // Check right side (2 bars after)
            for (int i = index; i < index + 2 && i < _low.Count; i++)
            {
                if (_low[i] < indexLow)
                    return null;
            }

            return indexLow;
        }
    }
}
