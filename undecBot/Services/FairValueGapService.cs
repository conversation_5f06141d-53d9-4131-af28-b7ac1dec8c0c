using System;
using System.Collections.Generic;
using System.Linq;
using cAlgo.API;
using UndecBot.Models;
using UndecBot.Configuration;

namespace UndecBot.Services
{
    /// <summary>
    /// Service responsible for detecting and managing Fair Value Gaps (FVGs)
    /// </summary>
    public class FairValueGapService
    {
        private readonly List<FVGZone> _fvgZones;
        private readonly DataSeries _high;
        private readonly DataSeries _low;
        private readonly TimeSeries _time;

        public FairValueGapService(DataSeries high, DataSeries low, TimeSeries time)
        {
            _fvgZones = new List<FVGZone>();
            _high = high ?? throw new ArgumentNullException(nameof(high));
            _low = low ?? throw new ArgumentNullException(nameof(low));
            _time = time ?? throw new ArgumentNullException(nameof(time));
        }

        /// <summary>
        /// Gets all detected FVG zones
        /// </summary>
        public IReadOnlyList<FVGZone> FVGZones => _fvgZones.AsReadOnly();

        /// <summary>
        /// Gets active FVG zones
        /// </summary>
        public IEnumerable<FVGZone> GetActiveFVGZones()
        {
            return _fvgZones.Where(fvg => fvg.IsActive);
        }

        /// <summary>
        /// Gets FVG zones by type (bullish/bearish)
        /// </summary>
        public IEnumerable<FVGZone> GetFVGZonesByType(bool isBullish)
        {
            return _fvgZones.Where(fvg => fvg.IsBullish == isBullish && fvg.IsActive);
        }

        /// <summary>
        /// Scans for Fair Value Gaps around a swing point
        /// </summary>
        public void ScanFairValueGaps(SwingPoint swingPoint)
        {
            if (swingPoint == null) return;

            int startIndex = swingPoint.Index;
            int endIndex = Math.Min(startIndex + BotConfiguration.MaxLookbackBars, _high.Count - 3);

            for (int i = startIndex; i <= endIndex; i++)
            {
                // Check for bearish FVG (after high swing)
                if (swingPoint.Type == SwingType.High && IsBearishFVG(i))
                {
                    CreateFVGZone(i + 2, _high[i + 3], _low[i + 1], false);
                }
                // Check for bullish FVG (after low swing)
                else if (swingPoint.Type == SwingType.Low && IsBullishFVG(i))
                {
                    CreateFVGZone(i + 2, _low[i + 3], _high[i + 1], true);
                }
            }
        }

        /// <summary>
        /// Updates FVG zones based on current price action
        /// </summary>
        public void UpdateFVGZones(double currentHigh, double currentLow)
        {
            foreach (var fvg in _fvgZones.Where(f => f.IsActive).ToList())
            {
                // Check if FVG has been filled
                if (IsFVGFilled(fvg, currentHigh, currentLow))
                {
                    fvg.IsActive = false;
                }
            }
        }

        /// <summary>
        /// Checks if price is currently within any active FVG zone
        /// </summary>
        public FVGZone GetFVGZoneAtPrice(double price)
        {
            return _fvgZones.FirstOrDefault(fvg => fvg.IsActive && fvg.ContainsPrice(price));
        }

        /// <summary>
        /// Cleans up old FVG zones to maintain performance
        /// </summary>
        public void CleanupOldFVGZones()
        {
            // Remove inactive zones first
            _fvgZones.RemoveAll(fvg => !fvg.IsActive);

            // If still too many, remove oldest active zones
            while (_fvgZones.Count > BotConfiguration.MaxFVGZones)
            {
                var oldestFVG = _fvgZones.OrderBy(fvg => fvg.Index).First();
                _fvgZones.Remove(oldestFVG);
            }
        }

        /// <summary>
        /// Gets the nearest FVG zone to a given price
        /// </summary>
        public FVGZone GetNearestFVGZone(double price, bool isBullish)
        {
            var relevantFVGs = GetFVGZonesByType(isBullish);
            
            return relevantFVGs
                .OrderBy(fvg => Math.Abs(fvg.GetMidPoint() - price))
                .FirstOrDefault();
        }

        private bool IsBearishFVG(int index)
        {
            if (index + 3 >= _high.Count) return false;
            
            // Bearish FVG: low[i+1] > high[i+3]
            return _low[index + 1] > _high[index + 3];
        }

        private bool IsBullishFVG(int index)
        {
            if (index + 3 >= _low.Count) return false;
            
            // Bullish FVG: high[i+1] < low[i+3]
            return _high[index + 1] < _low[index + 3];
        }

        private void CreateFVGZone(int index, double high, double low, bool isBullish)
        {
            if (_fvgZones.Count >= BotConfiguration.MaxFVGZones)
            {
                // Remove oldest FVG to make room
                var oldestFVG = _fvgZones.OrderBy(fvg => fvg.Index).First();
                _fvgZones.Remove(oldestFVG);
            }

            var fvgZone = new FVGZone(index, high, low, isBullish, _time[index]);
            
            // Avoid duplicate FVG zones
            if (!_fvgZones.Any(fvg => fvg.Equals(fvgZone)))
            {
                _fvgZones.Add(fvgZone);
            }
        }

        private bool IsFVGFilled(FVGZone fvg, double currentHigh, double currentLow)
        {
            if (fvg.IsBullish)
            {
                // Bullish FVG is filled when price moves below the low
                return currentLow <= fvg.Low;
            }
            else
            {
                // Bearish FVG is filled when price moves above the high
                return currentHigh >= fvg.High;
            }
        }
    }
}
