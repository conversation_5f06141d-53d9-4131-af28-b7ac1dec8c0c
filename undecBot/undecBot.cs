using System;
using System.Threading.Tasks;
using cAlgo.API;
using System.Linq;
using UndecBot.Services;
using UndecBot.Models;
using UndecBot.Configuration;

namespace UndecBot
{
    [Robot(AccessRights = AccessRights.None, AddIndicators = true)]
    public class UndecBot : Robot
    {
        [Parameter("Lots", DefaultValue = 0.01, MinValue = 0.01, Step = 0.01)]
        public double Lots { get; set; }

        [Parameter("SL", DefaultValue = 50, MinValue = 0, Step = 1)]
        public int SL { get; set; }

        [Parameter("TP", DefaultValue = 150, MinValue = 1, Step = 1)]
        public int TP { get; set; }

        [Parameter("Enable Trading", DefaultValue = true)]
        public bool EnableTrading { get; set; }

        [Parameter("Risk Percentage", DefaultValue = 1.0, MinValue = 0.1, MaxValue = 5.0, Step = 0.1)]
        public double RiskPercentage { get; set; }

        [Parameter("Use TradingAgents", DefaultValue = true)]
        public bool UseTradingAgents { get; set; }

        [Parameter("TradingAgents Confidence Threshold", DefaultValue = 0.7, MinValue = 0.1, MaxValue = 1.0, Step = 0.1)]
        public double TradingAgentsConfidenceThreshold { get; set; }

        [Parameter("TradingAgent API URL", DefaultValue = "http://tradingagent.undeclab.com")]
        public string TradingAgentApiUrl { get; set; }

        [Parameter("TradingAgent Timeout (seconds)", DefaultValue = 30, MinValue = 10, MaxValue = 120, Step = 5)]
        public int TradingAgentTimeoutSeconds { get; set; }

        // RISK MANAGEMENT PARAMETERS
        [Parameter("Max Risk Per Trade %", DefaultValue = 2.0, MinValue = 0.5, MaxValue = 10.0, Step = 0.1)]
        public double MaxRiskPerTrade { get; set; }

        [Parameter("Max Daily Drawdown %", DefaultValue = 5.0, MinValue = 1.0, MaxValue = 15.0, Step = 0.5)]
        public double MaxDailyDrawdown { get; set; }

        [Parameter("Max Concurrent Positions", DefaultValue = 3, MinValue = 1, MaxValue = 10, Step = 1)]
        public int MaxPositions { get; set; }

        [Parameter("Min Risk Reward Ratio", DefaultValue = 1.5, MinValue = 1.0, MaxValue = 5.0, Step = 0.1)]
        public double MinRiskReward { get; set; }

        // MULTI-TIMEFRAME ANALYSIS PARAMETERS
        [Parameter("HTF Confluence Required", DefaultValue = true)]
        public bool UseHTFConfluence { get; set; }

        [Parameter("Min Confluence Score", DefaultValue = 0.6, MinValue = 0.3, MaxValue = 1.0, Step = 0.1)]
        public double MinConfluenceScore { get; set; }

        [Parameter("Session Filter", DefaultValue = true)]
        public bool UseSessionFilter { get; set; }

        [Parameter("HTF Timeframes", DefaultValue = "H4,D1")]
        public string HTFTimeframes { get; set; }

        // PERFORMANCE OPTIMIZATION PARAMETERS
        [Parameter("Performance Monitoring", DefaultValue = true)]
        public bool EnablePerformanceMonitoring { get; set; }



        // Data series
        private DataSeries _high, _low, _open, _close;
        private TimeSeries _time;

        // Services
        private SwingPointDetectionService _swingPointService;
        private LiquiditySweepDetectionService _liquiditySweepService;
        private FairValueGapService _fvgService;
        private ChartDrawingService _drawingService;
        private TradingExecutionService _tradingService;
        private TradingAgentsApiService _tradingAgentsApiService;
        private TradingAgentsIntegrationService _tradingAgentsService;
        private RiskManagementService _riskService;
        private MarketStructureAnalysisService _marketStructureService;
        private PerformanceOptimizedBot _performanceService;

        protected override void OnStart()
        {
            // Initialize data series
            _high = Bars.HighPrices;
            _low = Bars.LowPrices;
            _open = Bars.OpenPrices;
            _close = Bars.ClosePrices;
            _time = Bars.OpenTimes;

            // Initialize services
            _swingPointService = new SwingPointDetectionService(_high, _low, _time);
            _liquiditySweepService = new LiquiditySweepDetectionService(_high, _low, _open, _close, _time);
            _fvgService = new FairValueGapService(_high, _low, _time);
            _drawingService = new ChartDrawingService(Chart, _time);
            _tradingService = new TradingExecutionService(this, Symbol.Name, _high, _low, _close, _time);
            _riskService = new RiskManagementService(this, Symbol.Name);
            _marketStructureService = new MarketStructureAnalysisService(this, _high, _low, _close, _time);
            _performanceService = new PerformanceOptimizedBot(BotConfiguration.CircularBufferSize);

            // Initialize TradingAgents services if enabled
            if (UseTradingAgents)
            {
                _tradingAgentsApiService = new TradingAgentsApiService(this, TradingAgentApiUrl, TradingAgentTimeoutSeconds);
                _tradingAgentsService = new TradingAgentsIntegrationService(this, Symbol.Name, _tradingAgentsApiService);
                Print($"TradingAgent integration enabled (API: {TradingAgentApiUrl}, Timeout: {TradingAgentTimeoutSeconds}s)");

                // Test TradingAgent connectivity
                Task.Run(async () => await TestTradingAgentConnectivity());
            }

            // Initialize HTF analysis if enabled
            if (UseHTFConfluence)
            {
                var timeframes = HTFTimeframes.Split(',').Select(tf => tf.Trim()).ToArray();
                _marketStructureService.InitializeHTFData(timeframes);
                Print($"📊 HTF Confluence enabled: {string.Join(", ", timeframes)}, Min Score: {MinConfluenceScore}");
            }

            Print($"UndecBot initialized successfully. Trading: {(EnableTrading ? "Enabled" : "Disabled")}, TradingAgent: {(UseTradingAgents ? "Enabled" : "Disabled")}");
            Print($"💰 Risk Management: Max Risk {MaxRiskPerTrade}%, Max DD {MaxDailyDrawdown}%, Max Positions {MaxPositions}, Min RR {MinRiskReward}");
            Print($"📈 Market Structure: HTF Confluence {(UseHTFConfluence ? "Enabled" : "Disabled")}, Session Filter {(UseSessionFilter ? "Enabled" : "Disabled")}");
            Print($"⚡ Performance Optimization: Enabled, Monitoring: {(EnablePerformanceMonitoring ? "On" : "Off")}, Buffer Size: {BotConfiguration.CircularBufferSize}");
        }

        protected override void OnBar()
        {
            try
            {
                // PERFORMANCE OPTIMIZATION: Direct synchronous processing for cTrader compatibility
                OnBarSync();
            }
            catch (Exception ex)
            {
                Print($"Error in OnBar: {ex.Message}");
            }
        }

        private void OnBarSync()
        {
            try
            {
                var currentBarCount = Bars.Count;

                // PERFORMANCE OPTIMIZATION: Skip processing if not enough bars
                if (currentBarCount < BotConfiguration.MaxLookbackBars) return;

                // PERFORMANCE OPTIMIZATION: Detect swing points (optimized with caching)
                _swingPointService.DetectSwingPoints(currentBarCount);

                // PERFORMANCE OPTIMIZATION: Process only new swing points using lazy enumeration
                var recentSwingPoints = _swingPointService.SwingPoints
                    .Where(sp => sp.Index >= currentBarCount - BotConfiguration.MaxLookbackBars)
                    .Take(5); // Limit to 5 most recent for performance

                foreach (var swingPoint in recentSwingPoints)
                {
                    var (isWickSweep, isBreakout) = _liquiditySweepService.DetectLiquiditySweeps(swingPoint);

                    if (isWickSweep)
                    {
                        ChartDrawingService.DrawSweepLine(swingPoint, Color.White, currentBarCount);

                        if (_liquiditySweepService.DetectOrderBlock(swingPoint))
                        {
                            _fvgService.ScanFairValueGaps(swingPoint);
                        }
                    }
                    else if (isBreakout)
                    {
                        ChartDrawingService.DrawSweepLine(swingPoint, Color.Blue, currentBarCount);

                        if (_liquiditySweepService.DetectOrderBlock(swingPoint))
                        {
                            _fvgService.ScanFairValueGaps(swingPoint);
                        }
                    }
                }

                // PERFORMANCE OPTIMIZATION: Draw order blocks only every few bars to reduce UI overhead
                if (currentBarCount % 3 == 0) // Draw every 3rd bar
                {
                    foreach (var orderBlock in PerformanceOptimizedBot.GetActiveOrderBlocksOptimized())
                    {
                        ChartDrawingService.DrawOrderBlock(orderBlock);
                    }
                }

                // PERFORMANCE OPTIMIZATION: Draw FVG zones less frequently
                if (currentBarCount % 2 == 0) // Draw every 2nd bar
                {
                    foreach (var fvgZone in _fvgService.GetActiveFVGZones())
                    {
                        ChartDrawingService.DrawFVGZone(fvgZone);
                    }
                }

                // PERFORMANCE OPTIMIZATION: Update FVG zones based on current price action
                if (currentBarCount > 1)
                {
                    _fvgService.UpdateFVGZones(_high.Last(), _low.Last());
                }

                // PERFORMANCE OPTIMIZATION: Execute trades if enabled (most critical path)
                if (EnableTrading)
                {
                    ExecuteTradesWithTradingAgents();
                }

                // PERFORMANCE OPTIMIZATION: Enhanced cleanup and monitoring
                if (Bars.Count % BotConfiguration.CacheCleanupInterval == 0)
                {
                    // Clean up all service caches
                    _swingPointService.CleanupOldSwingPoints();
                    _liquiditySweepService.CleanupOldOrderBlocks();
                    _fvgService.CleanupOldFVGZones();
                    MarketStructureAnalysisService.CleanupOldStructurePoints();

                    // NEW: Clean up trading service cache
                    _tradingService.CleanupCache();

                    // NEW: Clean up API service cache if available
                    if (_tradingAgentsApiService != null)
                    {
                        _tradingAgentsApiService.CleanupCache();
                    }

                    // Performance monitoring
                    var (processingTime, cacheHitRate, memoryUsage) = _performanceService.GetPerformanceMetrics();

                    // NEW: Get detailed performance metrics from services
                    var (cachedPositions, cachedTradeResults, tradingCacheValid) = _tradingService.GetPerformanceMetrics();
                    var (bufferCount, swingCacheSize, bufferUtilization) = _swingPointService.GetPerformanceMetrics();
                    var (highCount, lowCount) = _swingPointService.GetSwingPointCounts();

                    // Risk status report
                    double dailyPnL = _riskService.GetDailyPnL();
                    double dailyDD = _riskService.GetDailyDrawdownPercentage();

                    Print($"📊 Risk Status: Daily P&L: {dailyPnL:F2}, Daily DD: {dailyDD:F2}%, Positions: {Positions.Count}/{MaxPositions}");

                    // Enhanced performance monitoring
                    if (EnablePerformanceMonitoring)
                    {
                        Print($"⚡ Performance: Processing {processingTime}ms, Cache {cacheHitRate}%, Memory {memoryUsage}MB");
                        Print($"📈 Trading Cache: {cachedPositions} positions, {cachedTradeResults} results, Valid: {tradingCacheValid}");
                        Print($"🔄 Swing Points: {bufferCount} total ({highCount}H/{lowCount}L), Buffer: {bufferUtilization:F1}%, Cache: {swingCacheSize}");

                        // API performance metrics if available
                        if (_tradingAgentsApiService != null)
                        {
                            var (apiCachedResponses, rateLimitEntries, apiCacheHitRate) = _tradingAgentsApiService.GetPerformanceMetrics();
                            Print($"🌐 API Cache: {apiCachedResponses} responses, {rateLimitEntries} rate limits, Hit rate: {apiCacheHitRate:F1}%");
                        }

                        // Performance warning
                        if (processingTime > BotConfiguration.MaxProcessingTimeMs)
                        {
                            Print($"⚠️ Performance Warning: Processing time {processingTime}ms exceeds limit {BotConfiguration.MaxProcessingTimeMs}ms");
                        }

                        // Memory warning
                        if (memoryUsage > 100) // 100MB threshold
                        {
                            Print($"⚠️ Memory Warning: Usage {memoryUsage:F1}MB is high, consider reducing cache sizes");
                        }
                    }
                }

                // Update HTF analysis every 50 bars
                if (UseHTFConfluence && Bars.Count % 50 == 0)
                {
                    MarketStructureAnalysisService.UpdateHTFAnalysis();
                    var session = MarketStructureAnalysisService.GetCurrentTradingSession();
                    Print($"🌐 HTF Update: Session {session}, Confluence tracking active");
                }
            }
            catch (Exception ex)
            {
                Print($"Error in OnBarAsync: {ex.Message}");
            }
        }

        /// <summary>
        /// Executes trades using TradingAgent decisions combined with technical analysis (cTrader compatible)
        /// </summary>
        private void ExecuteTradesWithTradingAgents()
        {
            try
            {
                var activeOrderBlocks = _liquiditySweepService.GetActiveOrderBlocks().ToList();
                
                if (!activeOrderBlocks.Any())
                {
                    return; // No order blocks to trade
                }

                // Process each order block
                foreach (var orderBlock in activeOrderBlocks.Where(ob => ob.IsActive))
                {
                    ProcessOrderBlockWithEnhancements(orderBlock);
                }
            }
            catch (Exception ex)
            {
                Print($"Error in ExecuteTradesWithTradingAgents: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes a single order block with comprehensive analysis (cTrader compatible)
        /// </summary>
        private void ProcessOrderBlockWithEnhancements(OrderBlock orderBlock)
        {
            try
            {
                // 1. Risk Management Validation First
                if (!_riskService.ValidateTradeRisk(Lots, SL, TP, MaxRiskPerTrade, MaxDailyDrawdown, MaxPositions, MinRiskReward))
                {
                    return; // Risk validation failed
                }

                // 2. MULTI-TIMEFRAME SESSION FILTER
                if (UseSessionFilter && !MarketStructureAnalysisService.IsFavorableSession())
                {
                    Print("⏰ Trade rejected: Unfavorable trading session");
                    return;
                }

                // 3. MULTI-TIMEFRAME HTF CONFLUENCE ANALYSIS
                if (UseHTFConfluence)
                {
                    var timeframes = HTFTimeframes.Split(',').Select(tf => tf.Trim()).ToArray();
                    var confluenceKey = $"{orderBlock.Index}_{orderBlock.IsBullish}_{Bars.Count}";
                    
                    var confluenceAnalysis = _performanceService.GetCachedConfluence(confluenceKey, 
                        () => _marketStructureService.CalculateConfluence(orderBlock, timeframes));
                    
                    if (confluenceAnalysis.Score < MinConfluenceScore)
                    {
                        Print($"📊 Trade rejected: Confluence score {confluenceAnalysis.Score:F2} below threshold {MinConfluenceScore:F2}");
                        Print($"📋 HTF Analysis: {confluenceAnalysis.Analysis}");
                        return;
                    }
                    
                    Print($"✅ HTF Confluence approved: Score {confluenceAnalysis.Score:F2} - {confluenceAnalysis.Analysis}");
                    Print($"🎯 HTF Trend: {confluenceAnalysis.HTFTrend}, Session: {MarketStructureAnalysisService.GetCurrentTradingSession()}");
                }

                // 4. Check if we should execute trade based on TradingAgents and technical analysis
                bool shouldTrade = false;
                
                if (UseTradingAgents && _tradingAgentsService != null)
                {
                    // Use TradingAgent integration service with caching and advanced features (cTrader compatible)
                    shouldTrade = _tradingAgentsService.ShouldExecuteTrade(orderBlock, TradingAgentsConfidenceThreshold);
                }
                else
                {
                    // Fall back to pure technical analysis
                    shouldTrade = ShouldExecuteTradeBasedOnTechnicalAnalysis(orderBlock);
                }

                if (!shouldTrade)
                {
                    return;
                }

                // Calculate position size with TradingAgent risk adjustment if available
                double positionSize = Lots;
                if (UseTradingAgents && _tradingAgentsService != null)
                {
                    positionSize = _tradingAgentsService.GetRiskAdjustedPositionSize(Lots, RiskPercentage);
                }

                // Execute the trade
                TradeType tradeType = orderBlock.IsBullish ? TradeType.Buy : TradeType.Sell;
                string comment = $"HTF+TA+OB_{orderBlock.Index}_{(orderBlock.IsBullish ? "Bull" : "Bear")}";

                // Check if we already have a position for this order block
                var existingPositions = _tradingService.GetCurrentPositions(tradeType);
                if (existingPositions.Any(p => p.Label.Contains($"OB_{orderBlock.Index}")))
                {
                    return; // Already have a position for this order block
                }

                var result = _tradingService.ExecuteMarketOrder(tradeType, positionSize, SL, TP, comment);
                
                if (result.IsSuccessful)
                {
                    Print($"🚀 Multi-Timeframe trade executed: {tradeType} at {orderBlock.Price:F5} with size {positionSize:F3} for Order Block {orderBlock.Index}");
                }
                else
                {
                    Print($"❌ Trade failed: {result.Error} for Order Block {orderBlock.Index}");
                }
            }
            catch (Exception ex)
            {
                Print($"Error processing order block {orderBlock.Index}: {ex.Message}");
            }
        }

        /// <summary>
        /// Fallback method for technical analysis only
        /// </summary>
        private bool ShouldExecuteTradeBasedOnTechnicalAnalysis(OrderBlock orderBlock)
        {
            if (_close.Count < 2) return false;

            double currentHigh = _high[_high.Count - 1];
            double currentLow = _low[_low.Count - 1];
            double previousClose = _close[_close.Count - 2];

            return orderBlock.IsValidForEntry(0, currentHigh, currentLow, previousClose);
        }

        /// <summary>
        /// Tests TradingAgent connectivity and functionality
        /// </summary>
        private async Task TestTradingAgentConnectivity()
        {
            try
            {
                Print("🔍 Testing TradingAgent connectivity...");

                // First test API health
                var isHealthy = _tradingAgentsApiService.CheckHealth();
                if (!isHealthy)
                {
                    Print("❌ TradingAgent API health check failed");
                    return;
                }

                Print("✅ TradingAgent API is healthy");

                if (_tradingAgentsService == null)
                {
                    Print("❌ TradingAgent service is not initialized");
                    return;
                }

                // Test with a simple ticker (e.g., current symbol)
                string testTicker = Symbol.Name.Replace("_", "").ToUpper();
                Print($"📊 Testing TradingAgent decision for {testTicker}...");

                var decision = _tradingAgentsService.GetTradingDecision(testTicker);

                if (decision != null)
                {
                    Print($"✅ TradingAgent connectivity successful!");
                    Print($"📈 Decision: {decision.Action} (Confidence: {decision.Confidence:F2})");
                    Print($"🎯 Risk Level: {decision.RiskLevel}");
                    Print($"💭 Reasoning: {decision.Reasoning}");
                }
                else
                {
                    Print("⚠️ TradingAgent returned null decision - service may be unavailable");
                }
            }
            catch (Exception ex)
            {
                Print($"❌ TradingAgent connectivity test failed: {ex.Message}");
                Print("🔧 Check your TradingAgent configuration and network connectivity");
                Print("   Common issues:");
                Print($"   - TradingAgent API not running at {TradingAgentApiUrl}");
                Print("   - Network connectivity issues");
                Print("   - API timeout (current: {TradingAgentTimeoutSeconds}s)");
                Print("   - Missing API keys in TradingAgent server (OPENAI_API_KEY, etc.)");
            }
        }

        protected override void OnStop()
        {
            Print("UndecBot stopped.");
        }
    }
}
