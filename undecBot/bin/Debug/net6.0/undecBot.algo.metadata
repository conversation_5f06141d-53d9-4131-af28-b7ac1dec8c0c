[{"Flags": "Robot", "Guid": "00000000-0000-0000-0000-000000000000", "Group": "Custom", "FriendlyName": "UndecBot", "ShortName": null, "TypeName": "UndecBot.UndecBot", "AssemblyName": "undecBot, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null", "TimeZone": "UTC", "IsOverlay": false, "IsPercentage": false, "ScalePrecision": null, "Levels": [], "DefaultSymbolName": null, "DefaultTimeFrame": null, "Video": null, "Lines": [], "Clouds": [], "Parameters": [{"MinValue": 0.01, "MaxValue": 1.7976931348623157e+308, "Step": 0.01, "DefaultValue": 0.01, "ParameterType": "Double", "PropertyName": "Lots", "FriendlyName": "Lots", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 0, "MaxValue": 2147483647, "Step": 1, "DefaultValue": 50, "ParameterType": "Integer", "PropertyName": "SL", "FriendlyName": "SL", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 1, "MaxValue": 2147483647, "Step": 1, "DefaultValue": 150, "ParameterType": "Integer", "PropertyName": "TP", "FriendlyName": "TP", "GroupName": null, "IsValueVisibleInTitle": true}, {"DefaultValue": true, "ParameterType": "Boolean", "PropertyName": "EnableTrading", "FriendlyName": "Enable Trading", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 0.1, "MaxValue": 5.0, "Step": 0.1, "DefaultValue": 1.0, "ParameterType": "Double", "PropertyName": "RiskPercentage", "FriendlyName": "Risk Percentage", "GroupName": null, "IsValueVisibleInTitle": true}, {"DefaultValue": true, "ParameterType": "Boolean", "PropertyName": "UseTradingAgents", "FriendlyName": "Use TradingAgents", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 0.1, "MaxValue": 1.0, "Step": 0.1, "DefaultValue": 0.7, "ParameterType": "Double", "PropertyName": "TradingAgentsConfidenceThreshold", "FriendlyName": "TradingAgents Confidence Threshold", "GroupName": null, "IsValueVisibleInTitle": true}, {"DefaultValue": "http://tradingagent.undeclab.com", "ParameterType": "String", "PropertyName": "TradingAgentApiUrl", "FriendlyName": "TradingAgent API URL", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 10, "MaxValue": 120, "Step": 5, "DefaultValue": 30, "ParameterType": "Integer", "PropertyName": "TradingAgentTimeoutSeconds", "FriendlyName": "TradingAgent Timeout (seconds)", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 0.5, "MaxValue": 10.0, "Step": 0.1, "DefaultValue": 2.0, "ParameterType": "Double", "PropertyName": "MaxRiskPerTrade", "FriendlyName": "Max Risk Per Trade %", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 1.0, "MaxValue": 15.0, "Step": 0.5, "DefaultValue": 5.0, "ParameterType": "Double", "PropertyName": "MaxDailyDrawdown", "FriendlyName": "Max Daily Drawdown %", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 1, "MaxValue": 10, "Step": 1, "DefaultValue": 3, "ParameterType": "Integer", "PropertyName": "MaxPositions", "FriendlyName": "Max Concurrent Positions", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 1.0, "MaxValue": 5.0, "Step": 0.1, "DefaultValue": 1.5, "ParameterType": "Double", "PropertyName": "MinRiskReward", "FriendlyName": "<PERSON> <PERSON>", "GroupName": null, "IsValueVisibleInTitle": true}, {"DefaultValue": true, "ParameterType": "Boolean", "PropertyName": "UseHTFConfluence", "FriendlyName": "HTF Confluence Required", "GroupName": null, "IsValueVisibleInTitle": true}, {"MinValue": 0.3, "MaxValue": 1.0, "Step": 0.1, "DefaultValue": 0.6, "ParameterType": "Double", "PropertyName": "MinConfluenceScore", "FriendlyName": "Min Confluence Score", "GroupName": null, "IsValueVisibleInTitle": true}, {"DefaultValue": true, "ParameterType": "Boolean", "PropertyName": "UseSessionFilter", "FriendlyName": "Session Filter", "GroupName": null, "IsValueVisibleInTitle": true}, {"DefaultValue": "H4,D1", "ParameterType": "String", "PropertyName": "HTFTimeframes", "FriendlyName": "HTF Timeframes", "GroupName": null, "IsValueVisibleInTitle": true}, {"DefaultValue": true, "ParameterType": "Boolean", "PropertyName": "EnablePerformanceMonitoring", "FriendlyName": "Performance Monitoring", "GroupName": null, "IsValueVisibleInTitle": true}], "Capabilities": [], "CustomAttributes": [], "Sets": [], "AddIndicatorsToChart": true, "AdditionalInfoUrl": null}]