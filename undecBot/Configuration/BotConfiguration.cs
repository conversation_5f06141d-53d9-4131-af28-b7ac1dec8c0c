namespace UndecBot.Configuration
{
    /// <summary>
    /// Configuration class containing all bot parameters and constants
    /// </summary>
    public static class BotConfiguration
    {
        // === SWING POINT DETECTION ===
        /// <summary>
        /// Swing point range for detection
        /// </summary>
        public const int SwingPointRange = 5;

        /// <summary>
        /// Maximum lookback range for analysis
        /// </summary>
        public const int MaxLookbackBars = 7;

        // === LIQUIDITY SWEEP DETECTION ===
        /// <summary>
        /// Number of bars to look back for breakout confirmation
        /// </summary>
        public const int BreakoutLookback = 4;

        // === ORDER BLOCK DETECTION ===
        /// <summary>
        /// Maximum consecutive bars to check for impulse pattern
        /// </summary>
        public const int MaxConsecutiveBars = 5;

        /// <summary>
        /// Minimum consecutive bearish/bullish bars required for order block
        /// </summary>
        public const int MinConsecutiveBars = 2;

        /// <summary>
        /// Maximum bars to look ahead for impulse confirmation
        /// </summary>
        public const int MaxImpulseLookahead = 5;

        /// <summary>
        /// Extension length for order block lines (in bars)
        /// </summary>
        public const int OrderBlockExtension = 10;

        // === FAIR VALUE GAP ===
        /// <summary>
        /// Maximum number of FVG zones to keep on chart
        /// </summary>
        public const int MaxFVGZones = 3;

        // === COLLECTION LIMITS ===
        /// <summary>
        /// Maximum number of swing points to keep in memory
        /// </summary>
        public const int MaxSwingPoints = 70;

        /// <summary>
        /// Maximum number of order blocks to keep active
        /// </summary>
        public const int MaxOrderBlocks = 50;

        // === TRADING SESSION ===
        /// <summary>
        /// New York session start hour (UTC)
        /// </summary>
        public const int NewYorkSessionStart = 14;

        /// <summary>
        /// New York session end hour (UTC)
        /// </summary>
        public const int NewYorkSessionEnd = 19;

        // === CHART DRAWING ===
        /// <summary>
        /// Default line thickness for order blocks
        /// </summary>
        public const int OrderBlockLineThickness = 2;

        /// <summary>
        /// Default line thickness for sweep lines
        /// </summary>
        public const int SweepLineThickness = 1;

        /// <summary>
        /// Extension length for drawing utilities (in bars)
        /// </summary>
        public const int DrawingExtension = 15;

        // === PERFORMANCE ===
        /// <summary>
        /// Price comparison tolerance for floating point operations
        /// </summary>
        public const double PriceTolerance = 0.00001;

        /// <summary>
        /// Circular buffer size for performance optimization
        /// </summary>
        public const int CircularBufferSize = 1000;

        /// <summary>
        /// Cache cleanup interval in bars
        /// </summary>
        public const int CacheCleanupInterval = 100;

        /// <summary>
        /// Maximum processing time in milliseconds
        /// </summary>
        public const double MaxProcessingTimeMs = 1000.0;
    }
}
