# TradingAgent Refactoring Documentation

## Overview

This document describes the refactoring of the UndecBot to use the AgentServer TradingAgent instead of the previous TradingAgents implementation.

## Changes Made

### 1. Updated Models (`Models/TradingAgentsDecision.cs`)

**Added new models to match AgentServer TradingAgent API:**
- `MarketData` - Basic market data structure
- `EnhancedMarketData` - Extended market data with metadata
- `NewsItem` - News article information
- `EconomicIndicators` - Economic data indicators
- `MarketDataRequest` - Request parameters for market data
- `TradingAgentAnalysisResponse` - Response from TradingAgent analysis
- `AgentDetail` - Individual agent analysis details
- `DataProviderResponse<T>` - Generic response wrapper

### 2. New TradingAgent API Service (`Services/TradingAgentsApiService.cs`)

**Key Features:**
- HTTP client for communicating with AgentServer TradingAgent API
- Support for symbol analysis via `/analyze/symbol/{symbol}` endpoint
- Support for direct market data analysis via `/analyze` endpoint
- Health check functionality via `/analyze/health` endpoint
- Configurable timeout and base URL
- Robust error handling and logging

**Main Methods:**
- `AnalyzeSymbolAsync()` - Analyzes a symbol with automatic data fetching
- `AnalyzeMarketDataAsync()` - Analyzes provided market data directly
- `CheckHealthAsync()` - Checks API health status

### 3. Enhanced Integration Service (`Services/TradingAgentsIntegrationService.cs`)

**Key Features:**
- Caching mechanism for analysis results (5-minute expiry)
- Risk-adjusted position sizing based on agent confidence and consensus
- Order block alignment validation
- Multi-agent consensus scoring
- Comprehensive logging and error handling

**Main Methods:**
- `GetTradingDecisionAsync()` - Gets trading decision for a ticker
- `ShouldExecuteTradeAsync()` - Determines if trade should be executed based on order block
- `GetRiskAdjustedPositionSizeAsync()` - Calculates risk-adjusted position size
- `ClearCache()` - Clears analysis cache

### 4. Bot Configuration Updates (`undecBot.cs`)

**Parameter Changes:**
- Removed: `TradingAgents Path` parameter
- Added: `TradingAgent API URL` parameter (default: "http://localhost:5000")
- Added: `TradingAgent Timeout (seconds)` parameter (default: 30)

**Initialization Changes:**
- Updated service initialization to use new API URL and timeout parameters
- Enhanced connectivity testing with health checks
- Improved error messages and troubleshooting guidance

## Configuration

### Required Parameters

1. **TradingAgent API URL**: The base URL where the AgentServer TradingAgent is running
   - Default: `http://tradingagent.undeclab.com`
   - Example: `http://your-server:5000`

2. **TradingAgent Timeout**: HTTP request timeout in seconds
   - Default: `30` seconds
   - Range: 10-120 seconds

3. **TradingAgents Confidence Threshold**: Minimum confidence required for trade execution
   - Default: `0.7` (70%)
   - Range: 0.1-1.0

### AgentServer TradingAgent Setup

The bot now expects the AgentServer TradingAgent to be running and accessible at the configured URL. The TradingAgent should provide the following endpoints:

- `POST /analyze/symbol/{symbol}` - Analyze a symbol with automatic data fetching
- `POST /analyze` - Analyze provided market data
- `GET /analyze/health` - Health check endpoint

## Testing

A test class has been created at `Tests/TradingAgentIntegrationTest.cs` to verify the integration:

```csharp
// Test API connectivity
var success = await TradingAgentIntegrationTest.TestTradingAgentApiAsync("http://tradingagent.undeclab.com");

// Test integration service
var integrationSuccess = await TradingAgentIntegrationTest.TestIntegrationServiceAsync("http://tradingagent.undeclab.com");
```

## Benefits of the Refactoring

1. **Improved Architecture**: Clean separation between API communication and business logic
2. **Better Error Handling**: Comprehensive error handling and logging throughout
3. **Caching**: Analysis results are cached to reduce API calls and improve performance
4. **Flexibility**: Support for both symbol-based and direct market data analysis
5. **Risk Management**: Enhanced position sizing based on agent confidence and consensus
6. **Monitoring**: Health checks and detailed logging for troubleshooting

## Migration Notes

### From Old TradingAgents to New TradingAgent

1. **API Change**: The bot now uses HTTP REST API instead of Python subprocess calls
2. **Response Format**: Analysis responses now include multiple agent details and metadata
3. **Configuration**: Update bot parameters to point to the AgentServer TradingAgent URL
4. **Dependencies**: Ensure the AgentServer TradingAgent is running and accessible

### Backward Compatibility

The `TradingAgentsDecision` model has been preserved to maintain compatibility with existing code that expects this format. The integration service converts the new `TradingAgentAnalysisResponse` format to the legacy format as needed.

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure AgentServer TradingAgent is running at the configured URL
2. **Timeout Errors**: Increase the timeout parameter if analysis takes longer
3. **Health Check Failures**: Verify the TradingAgent API endpoints are accessible
4. **Analysis Returns Null**: Check TradingAgent logs for data provider issues

### Debugging

Enable detailed logging by monitoring the bot's Print statements, which include:
- API request URLs and parameters
- Response parsing details
- Cache hit/miss information
- Agent analysis details
- Error messages with troubleshooting guidance
